#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕共享数据精确分析脚本
专门检测真正的随机包名，避免误判正常应用
"""

import pandas as pd
import re
import json
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import chardet
from collections import Counter

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin1']
    
    detected_encoding = detect_encoding(file_path)
    if detected_encoding:
        encodings.insert(0, detected_encoding)
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用编码 {encoding} 读取文件")
            return df, encoding
        except Exception as e:
            continue
    
    raise Exception("无法读取文件，所有编码都失败了")

def get_suspicious_reason(pkg_name):
    """获取包名被判定为异常的具体原因"""
    reasons = []
    
    if not pkg_name or pkg_name == 'N/A':
        return "无效包名"
    
    # 精确匹配已知异常包名
    if pkg_name == 'com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi':
        reasons.append("已知恶意随机包名")
    
    # 检查明显的随机字符串模式
    if re.search(r'[A-Z][a-z]{3}\d{2}[A-Z][a-z]{2}[A-Z][a-z]{3}[A-Z]\d{2}[a-z]', pkg_name):
        reasons.append("包含类似DMey62ZwKlabJ48x的随机模式")
    
    if re.search(r'[A-Z]{3}[a-z]{4}[A-Z][a-z]{4}[A-Z][a-z]{2}[A-Z][a-z]{2}', pkg_name):
        reasons.append("包含类似VSTOgjnDFyx5EZmi的随机模式")
    
    # 检查超长包名
    if len(pkg_name) >= 40:
        reasons.append(f"超长包名({len(pkg_name)}个字符)")
    
    # 检查大量连续数字
    if re.search(r'\d{10,}', pkg_name):
        digit_match = re.search(r'\d{10,}', pkg_name)
        reasons.append(f"包含{len(digit_match.group())}位连续数字(疑似时间戳)")
    elif re.search(r'\d{8,}', pkg_name):
        digit_match = re.search(r'\d{8,}', pkg_name)
        reasons.append(f"包含{len(digit_match.group())}位连续数字")
    
    # 检查第三方平台生成的随机包名
    if re.search(r'org\.zywx\.wbpalmstar\.widgetone\.uex\d{8,}', pkg_name):
        reasons.append("第三方开发平台生成的随机包名")
    elif re.search(r'com\.apicloud\.[A-Z]\d{8,}', pkg_name):
        reasons.append("APICloud平台生成的随机包名")
    
    # 检查简单包名+大量数字的模式
    if re.search(r'com\.[a-z]{1,5}\d{8,}$', pkg_name):
        reasons.append("简单包名+大量数字的随机模式")
    
    # 检查包含超长英文单词
    if re.search(r'[a-z]{30,}', pkg_name):
        reasons.append("包含超长英文单词")
    
    # 检查特殊的随机模式
    if re.search(r'com\.[a-z]{1,3}\d+[a-z]{1,3}\.[a-z]{1,3}$', pkg_name):
        reasons.append("包含数字混合的短随机段")
    
    return "; ".join(reasons) if reasons else "其他异常模式"

def is_truly_random_package_name(pkg_name):
    """
    判断是否为真正的随机包名
    只检测最明显的异常模式，避免误判
    """
    if not pkg_name or pkg_name == 'N/A':
        return False
    
    # 精确匹配已知的恶意包名
    known_malicious = [
        'com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi',
    ]
    
    if pkg_name in known_malicious:
        return True
    
    # 只检测最明显的随机模式
    truly_random_patterns = [
        # 明显的随机字符串模式
        r'[A-Z][a-z]{3}\d{2}[A-Z][a-z]{2}[A-Z][a-z]{3}[A-Z]\d{2}[a-z]',  # DMey62ZwKlabJ48x
        r'[A-Z]{3}[a-z]{4}[A-Z][a-z]{4}[A-Z][a-z]{2}[A-Z][a-z]{2}',  # VSTOgjnDFyx5EZmi
        
        # 超长包名（40个字符以上）
        r'^.{40,}$',
        
        # 包含10位以上连续数字（疑似时间戳）
        r'\d{10,}',
        
        # 第三方平台生成的明显随机包名
        r'org\.zywx\.wbpalmstar\.widgetone\.uex\d{8,}',
        r'com\.apicloud\.[A-Z]\d{8,}',
        
        # 简单包名 + 8位以上数字
        r'com\.[a-z]{1,5}\d{8,}$',
        
        # 包含超长英文单词（30个字符以上）
        r'[a-z]{30,}',
        
        # 特殊的随机模式（如 com.g316522678.jnh）
        r'com\.[a-z]{1,3}\d{6,}[a-z]{1,3}\.[a-z]{1,5}$',
        
        # 包含明显随机字符串段的包名
        r'\.[A-Z][a-z]+\d{3,}[A-Z][a-z]+[A-Z]\d+[a-z]',
        r'\.[A-Z]{3,}[a-z]{4,}[A-Z][a-z]{4,}[A-Z][a-z]{2,}[A-Z][a-z]{2,}',
    ]
    
    return any(re.search(pattern, pkg_name) for pattern in truly_random_patterns)

def extract_package_names(app_info_str):
    """从应用信息字符串中提取包名"""
    if not app_info_str or app_info_str == 'N/A' or app_info_str == '[]':
        return []
    
    app_info_str = app_info_str.strip()
    package_names = []
    
    try:
        if app_info_str.startswith('{') and app_info_str.endswith('}'):
            app_data = json.loads(app_info_str)
            if 'apps' in app_data:
                for app in app_data['apps']:
                    if 'pkg_name' in app:
                        package_names.append(app['pkg_name'])
        
        elif app_info_str.startswith('['):
            pattern = r'[^(]+\(([a-zA-Z0-9._]+)\s*\|\s*[^)]*\)'
            matches = re.findall(pattern, app_info_str)
            package_names.extend(matches)
            
            if not matches:
                pattern2 = r'\(([^|)]+)\s*\|'
                matches2 = re.findall(pattern2, app_info_str)
                for match in matches2:
                    clean_match = match.strip()
                    if clean_match and '.' in clean_match:
                        package_names.append(clean_match)
    
    except Exception as e:
        print(f"解析应用信息失败: {e}")
    
    return package_names

def create_precise_analysis(csv_file_path, output_excel_path):
    """创建精确的分析结果"""
    print("开始精确分析屏幕共享数据...")
    
    # 读取CSV文件
    df, encoding = read_csv_with_encoding(csv_file_path)
    print(f"数据行数: {len(df)}")
    
    # 查找应用信息列
    app_info_column = None
    for col in df.columns:
        if '应用信息' in str(col):
            app_info_column = col
            break
    
    if app_info_column is None:
        for col in reversed(df.columns):
            if col and str(col).strip():
                app_info_column = col
                break
    
    print(f"使用应用信息列: {app_info_column}")
    
    # 分析数据
    suspicious_records = []
    all_suspicious_packages = []
    
    for index, row in df.iterrows():
        app_info = str(row[app_info_column]) if pd.notna(row[app_info_column]) else ''
        
        # 提取包名
        package_names = extract_package_names(app_info)
        
        # 检查是否有真正随机的包名
        suspicious_packages = []
        suspicious_reasons = []
        
        for pkg_name in package_names:
            if is_truly_random_package_name(pkg_name):
                suspicious_packages.append(pkg_name)
                all_suspicious_packages.append(pkg_name)
                reason = get_suspicious_reason(pkg_name)
                suspicious_reasons.append(f"{pkg_name}: {reason}")
        
        if suspicious_packages:
            # 只保留5个关键列 + 新增列
            record = {
                '时间': row['时间'] if '时间' in row else '',
                'udid': row['udid'] if 'udid' in row else '',
                '应用信息': app_info,
                '异常包名': ', '.join(suspicious_packages),
                '所有包名': ', '.join(package_names),
                '异常原因': '; '.join(suspicious_reasons)
            }
            suspicious_records.append(record)
    
    print(f"发现 {len(suspicious_records)} 条包含真正异常包名的记录")
    
    # 创建统计数据
    package_counter = Counter(all_suspicious_packages)
    total_suspicious_count = len(all_suspicious_packages)
    
    statistics_data = []
    for package, count in package_counter.most_common():
        percentage = (count / total_suspicious_count) * 100
        statistics_data.append({
            '异常包名': package,
            '出现次数': count,
            '占比百分比': f"{percentage:.2f}%"
        })
    
    return suspicious_records, statistics_data, len(df)

def create_excel_with_styles(suspicious_records, statistics_data, output_excel_path):
    """创建带样式的Excel文件"""
    if not suspicious_records:
        print("未发现异常包名")
        return

    # 创建DataFrame
    main_df = pd.DataFrame(suspicious_records)
    stats_df = pd.DataFrame(statistics_data)

    # 保存到Excel
    with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
        # 写入主数据
        main_df.to_excel(writer, sheet_name='异常包名详细数据', index=False)

        # 写入统计数据
        stats_df.to_excel(writer, sheet_name='统计数据', index=False)

        # 设置样式
        workbook = writer.book

        # 设置主数据工作表样式
        main_worksheet = writer.sheets['异常包名详细数据']
        set_worksheet_style(main_worksheet, '异常包名详细数据')

        # 设置统计数据工作表样式
        stats_worksheet = writer.sheets['统计数据']
        set_worksheet_style(stats_worksheet, '统计数据')

    print(f"精确分析结果已保存到: {output_excel_path}")

def set_worksheet_style(worksheet, sheet_type):
    """设置工作表样式"""
    # 设置标题行样式
    if sheet_type == '异常包名详细数据':
        header_fill = PatternFill(start_color='FF4757', end_color='FF4757', fill_type='solid')
    else:  # 统计数据
        header_fill = PatternFill(start_color='2ED573', end_color='2ED573', fill_type='solid')

    header_font = Font(color='FFFFFF', bold=True, size=12)
    header_alignment = Alignment(horizontal='center', vertical='center')

    # 设置边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 应用标题行样式
    for cell in worksheet[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # 设置数据行样式
    for row in worksheet.iter_rows(min_row=2):
        for cell in row:
            cell.border = thin_border
            cell.alignment = Alignment(vertical='center', wrap_text=True)

    # 自动调整列宽
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter

        for cell in column:
            try:
                if cell.value:
                    if column_letter == 'F' and sheet_type == '异常包名详细数据':  # 异常原因列
                        max_length = min(len(str(cell.value)), 80)
                    else:
                        max_length = max(max_length, len(str(cell.value)))
            except:
                pass

        # 设置列宽
        if sheet_type == '异常包名详细数据':
            if column_letter == 'C':  # 应用信息列
                adjusted_width = min(max_length + 2, 50)
            elif column_letter == 'F':  # 异常原因列
                adjusted_width = min(max_length + 2, 80)
            else:
                adjusted_width = min(max_length + 2, 35)
        else:  # 统计数据
            adjusted_width = min(max_length + 2, 50)

        worksheet.column_dimensions[column_letter].width = adjusted_width

    # 设置行高
    for row in worksheet.iter_rows():
        worksheet.row_dimensions[row[0].row].height = 30

def print_precise_summary(total_records, suspicious_records_count, statistics_data):
    """打印精确分析摘要"""
    print("\n" + "="*70)
    print("🎯 精确异常包名分析结果")
    print("="*70)
    print(f"总记录数: {total_records:,}")
    print(f"异常记录数: {suspicious_records_count:,}")
    print(f"异常比例: {suspicious_records_count/total_records*100:.2f}%")
    print(f"发现异常包名类型: {len(statistics_data)} 种")

    print("\n" + "="*70)
    print("🏆 异常包名统计排行榜")
    print("="*70)
    for i, stat in enumerate(statistics_data, 1):
        print(f"{i:2d}. {stat['异常包名']:<50} | {stat['出现次数']:3d} 次 | {stat['占比百分比']}")

    print("\n" + "="*70)
    print("📊 Excel文件内容说明")
    print("="*70)
    print("工作表1: 异常包名详细数据")
    print("  ├─ 时间: 记录时间")
    print("  ├─ udid: 设备唯一标识")
    print("  ├─ 应用信息: 完整的应用列表信息")
    print("  ├─ 异常包名: 检测到的异常包名")
    print("  ├─ 所有包名: 该记录中的所有应用包名")
    print("  └─ 异常原因: 详细的异常判定原因")
    print()
    print("工作表2: 统计数据")
    print("  ├─ 异常包名: 具体的异常包名")
    print("  ├─ 出现次数: 在所有异常记录中的出现次数")
    print("  └─ 占比百分比: 在所有异常包名中的占比")

if __name__ == "__main__":
    csv_file = "part-0.csv"
    output_file = "精确版异常包名分析结果.xlsx"

    try:
        print("🎯 开始精确版异常包名分析...")
        print("只检测最明显的随机包名，避免误判正常应用")
        print("-" * 50)

        suspicious_records, statistics_data, total_records = create_precise_analysis(csv_file, output_file)

        if suspicious_records:
            create_excel_with_styles(suspicious_records, statistics_data, output_file)
            print_precise_summary(total_records, len(suspicious_records), statistics_data)
        else:
            print("未发现真正的异常包名")

        print("\n✅ 精确分析完成！")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
