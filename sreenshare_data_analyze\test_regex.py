#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

# 测试数据
test_data = """[抖音(com.ss.android.ugc.aweme | N/A), 58同城(com.wuba | N/A), 唯品会(com.achievo.vipshop | N/A), QQ浏览器(com.tencent.mtt | N/A), 支付宝(com.eg.android.AlipayGphone | N/A), 知乎(com.zhihu.android | N/A), 360手机助手(com.qihoo.appstore | N/A), 微信(com.tencent.mm | N/A), 微博(com.sina.weibo | N/A), 系统界面(com.huawei.desktop.systemui | N/A), 拨号服务(com.android.phone | N/A), 喜马拉雅(com.ximalaya.ting.android | N/A), 设置存储(com.android.providers.settings | N/A), 快手(com.smile.gifmaker | N/A), 系统用户界面(com.android.systemui | N/A), 交管12123(com.tmri.app.main | N/A), 淘宝(com.taobao.taobao | N/A), 爱奇艺(com.qiyi.video | N/A), 大众点评(com.dianping.v1 | N/A), 我的60岁(com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi | N/A), 高德地图(com.autonavi.minimap | N/A)]"""

def test_extract_package_names(app_info_str):
    """测试包名提取"""
    if not app_info_str or app_info_str == 'N/A' or app_info_str == '[]':
        return []
    
    package_names = []
    
    print(f"测试数据: {app_info_str[:100]}...")
    
    # 尝试解析列表格式 [app_name(pkg_name | version), ...]
    if app_info_str.startswith('[') and app_info_str.endswith(']'):
        # 使用正则表达式提取包名
        # 匹配模式：应用名(包名 | 版本)
        pattern = r'[^(]+\(([a-zA-Z0-9._]+)\s*\|\s*[^)]*\)'
        matches = re.findall(pattern, app_info_str)
        print(f"模式1匹配结果: {matches[:5]}...")
        package_names.extend(matches)
        
        # 如果上面的模式没有匹配到，尝试更宽松的模式
        if not matches:
            # 匹配所有在括号内、竖线前的内容
            pattern2 = r'\(([^|)]+)\s*\|'
            matches2 = re.findall(pattern2, app_info_str)
            print(f"模式2匹配结果: {matches2[:5]}...")
            for match in matches2:
                # 清理匹配结果，去除空格
                clean_match = match.strip()
                if clean_match and '.' in clean_match:  # 包名通常包含点
                    package_names.append(clean_match)
    
    return package_names

# 测试
result = test_extract_package_names(test_data)
print(f"\n最终提取结果: {len(result)} 个包名")
print(f"前10个包名: {result[:10]}")

# 查找异常包名
suspicious_packages = []
for pkg in result:
    if 'DMey62ZwKlabJ48x' in pkg or 'VSTOgjnDFyx5EZmi' in pkg:
        suspicious_packages.append(pkg)
        print(f"发现异常包名: {pkg}")

print(f"\n异常包名数量: {len(suspicious_packages)}")
