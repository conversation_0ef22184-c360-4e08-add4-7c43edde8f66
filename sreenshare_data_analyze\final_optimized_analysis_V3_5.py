#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕共享数据异常包名分析程序 V3.5
在V3.4基础上修改统计报告表格式，重命名为异常包名应用统计表
"""

import pandas as pd
import re
import json
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import chardet
from collections import Counter
import sys

def detect_encoding(file_path):
    """检测文件编码"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB用于检测
            result = chardet.detect(raw_data)
            return result['encoding']
    except Exception as e:
        print(f"编码检测失败: {e}")
        return 'utf-8'

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['gb18030', 'gbk', 'gb2312', 'utf-8', 'utf-16', 'latin1']
    
    # 先尝试自动检测编码
    detected_encoding = detect_encoding(file_path)
    if detected_encoding and detected_encoding not in encodings:
        encodings.insert(0, detected_encoding)
    
    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"✅ 成功使用编码 {encoding} 读取文件")
            return df, encoding
        except Exception as e:
            print(f"❌ 编码 {encoding} 失败: {str(e)[:100]}")
            continue
    
    raise Exception("无法读取文件，所有编码都失败了")

def parse_app_info_v34(app_info_str):
    """
    解析应用信息字符串，提取应用名称和包名的配对关系 V3.4
    返回: (app_names_list, package_names_list, app_package_mapping)
    """
    if not app_info_str or app_info_str == 'N/A' or app_info_str.strip() == '[]':
        return [], [], {}
    
    app_info_str = app_info_str.strip()
    app_names = []
    package_names = []
    app_package_mapping = {}  # 应用名称与包名的对应关系
    
    try:
        # 处理JSON格式: {"apps":[{"app_name":"应用名","pkg_name":"包名"}]}
        if app_info_str.startswith('{') and app_info_str.endswith('}'):
            app_data = json.loads(app_info_str)
            if 'apps' in app_data and isinstance(app_data['apps'], list):
                for app in app_data['apps']:
                    if isinstance(app, dict):
                        app_name = app.get('app_name', '')
                        pkg_name = app.get('pkg_name', '')
                        if app_name and pkg_name:
                            app_names.append(app_name)
                            package_names.append(pkg_name)
                            # 建立对应关系
                            if app_name in app_package_mapping:
                                # 如果应用名称已存在，处理一对多的情况
                                if isinstance(app_package_mapping[app_name], list):
                                    app_package_mapping[app_name].append(pkg_name)
                                else:
                                    app_package_mapping[app_name] = [app_package_mapping[app_name], pkg_name]
                            else:
                                app_package_mapping[app_name] = pkg_name
        
        # 处理列表格式: [应用名(包名 | 版本), ...]
        elif app_info_str.startswith('['):
            # 使用正则表达式匹配: 应用名(包名 | 版本)
            pattern = r'([^(]+)\(([^|)]+)\s*\|\s*[^)]*\)'
            matches = re.findall(pattern, app_info_str)
            
            for app_name, pkg_name in matches:
                app_name = app_name.strip()
                pkg_name = pkg_name.strip()
                if app_name and pkg_name and '.' in pkg_name:  # 包名通常包含点
                    app_names.append(app_name)
                    package_names.append(pkg_name)
                    # 建立对应关系
                    if app_name in app_package_mapping:
                        # 如果应用名称已存在，处理一对多的情况
                        if isinstance(app_package_mapping[app_name], list):
                            app_package_mapping[app_name].append(pkg_name)
                        else:
                            app_package_mapping[app_name] = [app_package_mapping[app_name], pkg_name]
                    else:
                        app_package_mapping[app_name] = pkg_name
            
            # 如果上面的模式没有匹配到，尝试更宽松的模式
            if not matches:
                pattern2 = r'([^(]+)\(([^|)]+)\s*\|'
                matches2 = re.findall(pattern2, app_info_str)
                for app_name, pkg_name in matches2:
                    app_name = app_name.strip()
                    pkg_name = pkg_name.strip()
                    if app_name and pkg_name and '.' in pkg_name:
                        app_names.append(app_name)
                        package_names.append(pkg_name)
                        # 建立对应关系
                        if app_name in app_package_mapping:
                            if isinstance(app_package_mapping[app_name], list):
                                app_package_mapping[app_name].append(pkg_name)
                            else:
                                app_package_mapping[app_name] = [app_package_mapping[app_name], pkg_name]
                        else:
                            app_package_mapping[app_name] = pkg_name
    
    except Exception as e:
        print(f"解析应用信息失败: {e}")
        print(f"原始数据: {app_info_str[:200]}...")
    
    return app_names, package_names, app_package_mapping

def detect_suspicious_app_names(app_names):
    """
    检测异常应用名称 V3.4新增功能
    返回: (suspicious_app_names, app_keyword_mapping)
    """
    if not app_names:
        return [], {}
    
    # 定义异常应用关键词
    suspicious_keywords = [
        '极速通服务',
        '全时通服务',
        '协助通信',
        '天e云',
        '微会议',
        'U会议',
        'n会议',
        '书签',
        '企聊',
        '企达',
        '信书',
        '信通',
        '公信',
        '云信',
        '达信',
        'ROOM',
        'AVPlayer',
        'AXVPlayer',
        'AXYPlayer',
        'CDplayer',
    ]
    
    suspicious_app_names = []
    app_keyword_mapping = {}  # 记录每个异常应用对应的关键词
    
    for app_name in app_names:
        if not app_name:
            continue
        
        app_name_str = str(app_name)
        
        # 检查每个关键词
        for keyword in suspicious_keywords:
            if keyword in app_name_str:
                suspicious_app_names.append(app_name_str)
                app_keyword_mapping[app_name_str] = keyword
                break  # 找到一个匹配就跳出，避免重复
        
        # 特殊处理：思欲加速的多种分隔符变体
        siyu_patterns = [
            r'思欲.{0,2}加速',  # 思欲加速、思欲*加速、思欲_加速、思欲 加速等
        ]
        
        for pattern in siyu_patterns:
            if re.search(pattern, app_name_str):
                suspicious_app_names.append(app_name_str)
                app_keyword_mapping[app_name_str] = '思欲加速'
                break
        
        # 特殊处理：akPlayer/AKPlayer（不区分大小写）
        if re.search(r'akplayer', app_name_str, re.IGNORECASE):
            suspicious_app_names.append(app_name_str)
            app_keyword_mapping[app_name_str] = 'akPlayer'
    
    return suspicious_app_names, app_keyword_mapping

def format_app_package_mapping(app_package_mapping):
    """
    将应用名称与包名的对应关系格式化为JSON字符串
    """
    if not app_package_mapping:
        return "{}"
    
    try:
        # 确保JSON格式正确，处理特殊字符
        formatted_mapping = {}
        for app_name, pkg_name in app_package_mapping.items():
            # 清理应用名称中的特殊字符
            clean_app_name = str(app_name).replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
            
            if isinstance(pkg_name, list):
                # 处理一对多的情况
                clean_pkg_names = []
                for pkg in pkg_name:
                    clean_pkg = str(pkg).replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
                    clean_pkg_names.append(clean_pkg)
                formatted_mapping[clean_app_name] = clean_pkg_names
            else:
                # 处理一对一的情况
                clean_pkg_name = str(pkg_name).replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
                formatted_mapping[clean_app_name] = clean_pkg_name
        
        # 转换为JSON字符串，确保中文正确显示
        return json.dumps(formatted_mapping, ensure_ascii=False, separators=(',', ':'))
    
    except Exception as e:
        print(f"格式化应用包名对应关系失败: {e}")
        return "{}"

def format_suspicious_apps_and_packages(suspicious_packages, app_package_mapping):
    """
    格式化异常应用和包名为JSON格式
    V3.3功能：根据异常包名找到对应的应用名称，生成JSON格式
    """
    if not suspicious_packages:
        return "{}"
    
    try:
        suspicious_mapping = {}
        
        # 创建包名到应用名称的反向映射
        pkg_to_app_mapping = {}
        for app_name, pkg_name in app_package_mapping.items():
            if isinstance(pkg_name, list):
                for pkg in pkg_name:
                    if pkg not in pkg_to_app_mapping:
                        pkg_to_app_mapping[pkg] = []
                    pkg_to_app_mapping[pkg].append(app_name)
            else:
                if pkg_name not in pkg_to_app_mapping:
                    pkg_to_app_mapping[pkg_name] = []
                pkg_to_app_mapping[pkg_name].append(app_name)
        
        # 为每个异常包名找到对应的应用名称
        for pkg_name in suspicious_packages:
            clean_pkg_name = str(pkg_name).replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
            
            if pkg_name in pkg_to_app_mapping:
                # 找到了对应的应用名称
                app_names = pkg_to_app_mapping[pkg_name]
                for app_name in app_names:
                    clean_app_name = str(app_name).replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
                    suspicious_mapping[clean_app_name] = clean_pkg_name
            else:
                # 没有找到对应的应用名称，使用包名作为键
                suspicious_mapping[f"未知应用({clean_pkg_name})"] = clean_pkg_name
        
        # 转换为JSON字符串
        return json.dumps(suspicious_mapping, ensure_ascii=False, separators=(',', ':'))
    
    except Exception as e:
        print(f"格式化异常应用和包名失败: {e}")
        return "{}"

def is_suspicious_package_name_v34(pkg_name):
    """
    判断是否为异常包名 V3.4
    继承V3.3的检测规则
    """
    if not pkg_name or pkg_name == 'N/A':
        return False, ""

    # 扩展的正常包名白名单（避免误判）
    normal_patterns = [
        r'^com\.android\..*',  # Android系统包名
        r'^com\.google\..*',   # Google官方应用
        r'^com\.samsung\..*',  # 三星官方应用
        r'^com\.huawei\..*',   # 华为官方应用
        r'^com\.xiaomi\..*',   # 小米官方应用
        r'^com\.oppo\..*',     # OPPO官方应用
        r'^com\.vivo\..*',     # VIVO官方应用
        r'^com\.tencent\..*',  # 腾讯系列应用
        r'^com\.alibaba\..*',  # 阿里系列应用
        r'^com\.baidu\..*',    # 百度系列应用
        r'^cn\..*',            # 中国域名包名
        r'^org\..*',           # 组织域名包名
        r'^net\..*',           # 网络域名包名
        r'^edu\..*',           # 教育域名包名
        r'^gov\..*',           # 政府域名包名
        # 常见的正常应用包名（精确匹配）
        r'^com\.(icbc|unionpay|wuba|videogo|paem|htinns|mosheng|nbbank|forms|yipiao|yoosee|wlqq|jin10|mymoney|szzc|ehai|zxscnew|booking|changba|jxedt)$',
        # 其他常见的正常包名模式
        r'^com\.[a-z]{5,}$',    # 5个字符以上的单词通常是正常的
        r'^com\.[a-z]+\.[a-z]{3,}$',  # 包含两个单词段的通常是正常的
    ]

    # 检查是否在白名单中
    for pattern in normal_patterns:
        if re.match(pattern, pkg_name):
            return False, ""

    # V2.0原有的异常模式检测
    v2_suspicious_patterns = [
        # 1. 精确匹配已知的恶意包名
        (r'^com\.DMey62ZwKlabJ48x\.VSTOgjnDFyx5EZmi$', "已知恶意随机包名"),

        # 2. 明显的随机字符串模式
        (r'[A-Z][a-z]{3}\d{2}[A-Z][a-z]{2}[A-Z][a-z]{3}[A-Z]\d{2}[a-z]', "包含类似DMey62ZwKlabJ48x的随机模式"),
        (r'[A-Z]{3}[a-z]{4}[A-Z][a-z]{4}[A-Z][a-z]{2}[A-Z][a-z]{2}', "包含类似VSTOgjnDFyx5EZmi的随机模式"),

        # 3. 包含时间戳的包名（10位数字，通常是Unix时间戳）
        (r'\.hd\d{10}$', "包含时间戳标识的包名"),
        (r'\.lt\d{10}$', "包含时间戳标识的包名"),
        (r'\.m\d{10}$', "包含时间戳标识的包名"),
        (r'com\.a\d{10}$', "包含时间戳的简单随机包名"),
        (r'com\.tiktok\.dy\d{8}$', "TikTok相关的时间戳包名"),

        # 4. 第三方平台生成的随机包名
        (r'org\.zywx\.wbpalmstar\.widgetone\.uex\d{8,}$', "正益无线平台生成的随机包名"),
        (r'com\.apicloud\.[A-Z]\d{8,}$', "APICloud平台生成的随机包名"),

        # 5. 包含大量数字的简单随机包名
        (r'com\.g\d{8,}\.[a-z]{1,5}$', "包含大量数字的简单随机包名"),
        (r'com\.network\.xf\d{8,}$', "网络相关的随机包名"),

        # 6. 复杂的多段随机字符串
        (r'com\.ndc[a-z0-9]{6,}\.nec[a-z0-9]{6,}\.jeb[a-z0-9]{6,}\.nd[a-z0-9]{10,}', "包含多段随机字符串的复杂包名"),

        # 7. 包含明显随机字符串段的包名
        (r'\.[A-Z][a-z]+\d{3,}[A-Z][a-z]+[A-Z]\d+[a-z]', "包含随机字符串段"),
        (r'\.[A-Z]{3,}[a-z]{4,}[A-Z][a-z]{4,}[A-Z][a-z]{2,}[A-Z][a-z]{2,}', "包含复杂随机字符串段"),

        # 8. 字节跳动小程序相关的随机包名
        (r'com\.bytedance\.tt[a-z0-9]{16,}\.miniapk$', "字节跳动小程序随机包名"),

        # 9. 包含明显拼写错误或随机组合的英文单词
        (r'com\.[a-z]+\.(conditionerremotecontrold|mobilephonelocationtracking|mobilephonepositioningartifact)$', "包含拼写错误的英文单词"),
        (r'com\.saoyisaoruanjiansaosaogengjiankang$', "包含超长中文拼音组合"),

        # 10. 其他明显的随机模式
        (r'com\.fallturgggye\.spentyyrtudid\.fjvghbbhallturf$', "明显的随机字符串组合"),
        (r'com\.mo3ph0o\.m\d{10}$', "包含随机字符和时间戳的包名"),
    ]

    # V3.1精确的新增异常模式检测
    v31_new_patterns = [
        # 1. 极短的随机组合包名
        (r'^com\.[a-z0-9]{3}$', "3字符随机包名"),

        # 2. 字母数字混合的明显随机包名
        (r'^com\.[a-z]{1,2}[0-9]{1,2}[a-z]{1,2}$', "字母数字混合的随机包名"),

        # 3. 无逻辑结构的随机包名
        (r'^com\.[a-z]{2,3}[0-9]{1,2}[a-z]{1,2}\.(app|test|tmp)$', "缺乏可读性的无逻辑结构包名"),

        # 4. 批量生成的马甲包名（严格模式）
        (r'^com\.[a-z]{1,2}[0-9]{4,6}[a-z]{0,2}$', "疑似批量生成的马甲包名"),

        # 5. 临时测试包名
        (r'^com\.(test|tmp|temp|demo)[a-z0-9]{2,8}$', "临时测试或分身应用包名"),

        # 6. 反编译工具生成的随机包名
        (r'^com\.[a-z]{1,2}[0-9]{2,4}[a-z]{1,2}\.[a-z]{1,3}[0-9]{1,3}$', "疑似反编译工具随机替换的包名"),

        # 7. 无元音字母的随机包名
        (r'^com\.[bcdfghjklmnpqrstvwxyz]{3,4}$', "无元音字母的随机包名"),

        # 8. 字母数字交替的随机包名
        (r'^com\.[a-z]{1,2}[0-9][a-z][0-9]$', "字母数字交替的随机包名"),
    ]

    # 合并所有检测模式
    all_patterns = v2_suspicious_patterns + v31_new_patterns

    # 检查每个可疑模式
    for pattern, reason in all_patterns:
        if re.search(pattern, pkg_name):
            return True, reason

    return False, ""

def analyze_screen_share_data_v34(csv_file_path, output_excel_path):
    """
    分析屏幕共享数据 V3.4
    新增异常应用名称检测功能
    """
    print("🚀 开始屏幕共享数据异常包名分析 V3.5")
    print("🔄 修改统计报告表格式，重命名为异常包名应用统计表")
    print("=" * 70)

    # 1. 读取CSV文件
    print("📖 正在读取CSV文件...")
    df, encoding = read_csv_with_encoding(csv_file_path)
    print(f"✅ 数据读取完成，共 {len(df):,} 行数据")
    print(f"📊 数据列数: {len(df.columns)}")

    # 2. 查找应用信息列
    print("\n🔍 正在查找应用信息列...")
    app_info_column = None
    for col in df.columns:
        if '应用信息' in str(col):
            app_info_column = col
            break

    if app_info_column is None:
        # 查找最后一个非空列名的列（通常是应用信息）
        for col in reversed(df.columns):
            if col and str(col).strip():
                app_info_column = col
                break

    if app_info_column is None:
        raise Exception("未找到应用信息列")

    print(f"✅ 找到应用信息列: {app_info_column}")

    # 3. 解析应用信息并检测异常包名和异常应用名称
    print("\n🔬 正在解析应用信息并检测异常包名...")
    print("🆕 同时检测异常应用名称...")

    results = []
    all_suspicious_packages = []
    all_suspicious_app_names = []
    all_app_keyword_mappings = {}  # 记录所有异常应用对应的关键词
    processed_count = 0
    v2_detected = 0  # V2.0规则检测到的数量
    v31_new_detected = 0  # V3.1新规则检测到的数量

    for index, row in df.iterrows():
        processed_count += 1

        # 显示进度
        if processed_count % 1000 == 0:
            print(f"⏳ 已处理 {processed_count:,}/{len(df):,} 行数据 ({processed_count/len(df)*100:.1f}%)")

        # 获取应用信息
        app_info = str(row[app_info_column]) if pd.notna(row[app_info_column]) else ''

        # 解析应用信息（V3.4版本，包含对应关系）
        app_names, package_names, app_package_mapping = parse_app_info_v34(app_info)

        # 检测异常包名
        suspicious_packages = []
        suspicious_reasons = []

        for pkg_name in package_names:
            is_suspicious, reason = is_suspicious_package_name_v34(pkg_name)
            if is_suspicious:
                suspicious_packages.append(pkg_name)
                suspicious_reasons.append(f"{pkg_name}:{reason}")
                all_suspicious_packages.append(pkg_name)

                # 统计V2.0和V3.1新规则的检测效果
                if any(pattern in reason for pattern in ["3字符随机", "字母数字混合", "缺乏可读性", "疑似批量生成", "临时测试", "疑似反编译工具", "无元音字母", "字母数字交替"]):
                    v31_new_detected += 1
                else:
                    v2_detected += 1

        # V3.4新增：检测异常应用名称
        suspicious_app_names, app_keyword_mapping = detect_suspicious_app_names(app_names)
        all_suspicious_app_names.extend(suspicious_app_names)
        all_app_keyword_mappings.update(app_keyword_mapping)

        # 构建结果记录（V3.4版本，新增异常应用列）
        result_record = {
            '时间': row.get('时间', ''),
            'udid': row.get('udid', ''),
            '应用信息': app_info,
            '所有应用名称': ', '.join(app_names) if app_names else '',
            '所有包名': ', '.join(package_names) if package_names else '',
            '应用名称与包名对应关系': format_app_package_mapping(app_package_mapping),
            '异常应用和包名': format_suspicious_apps_and_packages(suspicious_packages, app_package_mapping),
            '异常应用': ', '.join(suspicious_app_names) if suspicious_app_names else '',  # V3.4新增列
            '异常包名原因': '; '.join(suspicious_reasons) if suspicious_reasons else ''
        }

        results.append(result_record)

    print(f"✅ 数据解析完成，共处理 {len(results):,} 行数据")
    print(f"📊 V2.0规则检测到: {v2_detected} 个异常包名")
    print(f"🔧 V3.1规则检测到: {v31_new_detected} 个异常包名")
    print(f"🆕 检测到异常应用: {len(all_suspicious_app_names)} 个")

    # 4. 创建异常包名统计数据
    print("\n📊 正在生成异常包名统计数据...")
    package_counter = Counter(all_suspicious_packages)
    total_suspicious_count = len(all_suspicious_packages)

    # 创建包名到应用名称的反向映射（用于统计表）
    pkg_to_app_mapping_for_stats = {}
    for result in results:
        if result['异常应用和包名'] and result['异常应用和包名'] != '{}':
            try:
                suspicious_mapping = json.loads(result['异常应用和包名'])
                for app_name, pkg_name in suspicious_mapping.items():
                    if pkg_name not in pkg_to_app_mapping_for_stats:
                        pkg_to_app_mapping_for_stats[pkg_name] = []
                    if app_name not in pkg_to_app_mapping_for_stats[pkg_name]:
                        pkg_to_app_mapping_for_stats[pkg_name].append(app_name)
            except:
                continue

    package_statistics_data = []
    for package, count in package_counter.most_common():
        percentage = (count / total_suspicious_count) * 100 if total_suspicious_count > 0 else 0

        # 获取包名的检测原因
        _, reason = is_suspicious_package_name_v34(package)

        # 格式化应用和异常包名
        if package in pkg_to_app_mapping_for_stats:
            app_names = pkg_to_app_mapping_for_stats[package]
            app_package_format = f"{app_names[0]}：{package}"  # 取第一个应用名称
        else:
            app_package_format = f"未知应用：{package}"

        package_statistics_data.append({
            '应用和异常包名': app_package_format,
            '出现次数': count,
            '占比百分比': f"{percentage:.2f}%",
            '检测规则': reason
        })

    # 5. V3.4新增：创建异常应用统计数据
    print("🆕 正在生成异常应用统计数据...")
    keyword_counter = Counter(all_app_keyword_mappings.values())
    total_app_suspicious_count = len(all_suspicious_app_names)

    app_statistics_data = []
    for keyword, count in keyword_counter.most_common():
        percentage = (count / total_app_suspicious_count) * 100 if total_app_suspicious_count > 0 else 0

        app_statistics_data.append({
            '异常应用关键词': keyword,
            '命中次数': count,
            '占比百分比': f"{percentage:.2f}%"
        })

    print(f"✅ 统计数据生成完成")
    print(f"📈 发现异常包名类型: {len(package_statistics_data)} 种")
    print(f"📈 异常包名总出现次数: {total_suspicious_count:,} 次")
    print(f"🆕 发现异常应用关键词类型: {len(app_statistics_data)} 种")
    print(f"🆕 异常应用总出现次数: {total_app_suspicious_count:,} 次")

    # 6. 生成Excel文件
    print(f"\n📝 正在生成Excel文件: {output_excel_path}")
    create_excel_report_v34(results, package_statistics_data, app_statistics_data, output_excel_path, v2_detected, v31_new_detected, total_app_suspicious_count)

    # 7. 打印分析摘要
    print_analysis_summary_v34(len(df), len([r for r in results if r['异常应用和包名'] and r['异常应用和包名'] != '{}']),
                              len([r for r in results if r['异常应用']]), package_statistics_data, app_statistics_data, v2_detected, v31_new_detected)

    return results, package_statistics_data, app_statistics_data, v2_detected, v31_new_detected

def create_excel_report_v34(results, package_statistics_data, app_statistics_data, output_excel_path, v2_detected, v31_new_detected, total_app_suspicious_count):
    """创建Excel报告 V3.4"""
    try:
        # 创建DataFrame
        main_df = pd.DataFrame(results)
        package_stats_df = pd.DataFrame(package_statistics_data)
        app_stats_df = pd.DataFrame(app_statistics_data)

        # 保存到Excel
        with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
            # 写入主数据
            main_df.to_excel(writer, sheet_name='主数据表', index=False)

            # 写入异常包名统计数据
            if not package_stats_df.empty:
                package_stats_df.to_excel(writer, sheet_name='异常包名应用统计表', index=False)

            # V3.4新增：写入异常应用统计数据
            if not app_stats_df.empty:
                app_stats_df.to_excel(writer, sheet_name='异常应用统计报告', index=False)

            # 创建V3.5改进说明工作表
            create_v35_improvement_sheet(writer, v2_detected, v31_new_detected, len(package_statistics_data), len(app_statistics_data), total_app_suspicious_count)

            # 设置样式
            workbook = writer.book

            # 设置主数据表样式
            main_worksheet = writer.sheets['主数据表']
            set_worksheet_style_v34(main_worksheet, '主数据表')

            # 设置异常包名统计报告表样式
            if not package_stats_df.empty:
                package_stats_worksheet = writer.sheets['异常包名应用统计表']
                set_worksheet_style_v34(package_stats_worksheet, '异常包名应用统计表')

            # V3.4新增：设置异常应用统计报告表样式
            if not app_stats_df.empty:
                app_stats_worksheet = writer.sheets['异常应用统计报告']
                set_worksheet_style_v34(app_stats_worksheet, '异常应用统计报告')

            # 设置改进说明表样式
            improvement_worksheet = writer.sheets['V3.5改进说明']
            set_worksheet_style_v34(improvement_worksheet, 'V3.5改进说明')

        print(f"✅ Excel文件生成成功: {output_excel_path}")

    except Exception as e:
        print(f"❌ Excel文件生成失败: {e}")
        raise

def create_v35_improvement_sheet(writer, v2_detected, v31_new_detected, package_types, app_keyword_types, total_app_suspicious_count):
    """创建V3.4改进说明工作表"""
    improvement_data = [
        ['V3.5版本改进说明', ''],
        ['', ''],
        ['改进项目', '说明'],
        ['版本特点', '新增异常应用名称检测功能'],
        ['新增列名', '异常应用'],
        ['重命名工作表', '统计报告表 → 异常包名应用统计表'],
        ['新增工作表', '异常应用统计报告'],
        ['修改列名', '异常包名 → 应用和异常包名'],
        ['输出格式', '应用名称：包名'],
        ['V2.0规则检测到的异常包名', f'{v2_detected}个'],
        ['V3.1规则检测到的异常包名', f'{v31_new_detected}个'],
        ['总异常包名类型', f'{package_types}种'],
        ['异常应用关键词类型', f'{app_keyword_types}种'],
        ['异常应用总出现次数', f'{total_app_suspicious_count}次'],
        ['', ''],
        ['V3.4主要改进', ''],
        ['', ''],
        ['改进1', '新增异常应用名称检测功能'],
        ['检测范围', '在"所有应用名称"列中检测异常应用'],
        ['检测方式', '基于关键词模糊匹配'],
        ['', ''],
        ['改进2', '新增"异常应用"列'],
        ['位置', '在"异常应用和包名"列之后'],
        ['格式', '逗号分隔的异常应用名称'],
        ['', ''],
        ['改进3', '新增"异常应用统计报告"工作表'],
        ['内容', '异常应用关键词统计分析'],
        ['排序', '按命中次数降序排列'],
        ['', ''],
        ['异常应用检测关键词', ''],
        ['', ''],
        ['通信类', '极速通服务、全时通服务、协助通信'],
        ['会议类', '微会议、U会议、n会议、天e云'],
        ['办公类', '书签、企聊、企达'],
        ['通信工具', '信书、信通、公信、云信、达信'],
        ['播放器类', 'AVPlayer、AXVPlayer、AXYPlayer、CDplayer'],
        ['特殊匹配', 'akPlayer/AKPlayer（不区分大小写）'],
        ['变体支持', '思欲加速（支持多种分隔符）'],
        ['房间类', 'ROOM'],
        ['', ''],
        ['技术实现', ''],
        ['', ''],
        ['检测方式', '模糊匹配，包含关键词即命中'],
        ['特殊处理1', '思欲加速支持*/_/空格等分隔符'],
        ['特殊处理2', 'akPlayer不区分大小写匹配'],
        ['统计方式', '按关键词统计命中次数和占比'],
        ['输出格式', '逗号分隔的异常应用名称列表'],
    ]

    improvement_df = pd.DataFrame(improvement_data, columns=['项目', '内容'])
    improvement_df.to_excel(writer, sheet_name='V3.5改进说明', index=False)

def set_worksheet_style_v34(worksheet, sheet_type):
    """设置工作表样式 V3.4"""
    # 设置标题行样式
    if sheet_type == '主数据表':
        header_fill = PatternFill(start_color='1ABC9C', end_color='1ABC9C', fill_type='solid')  # 青绿色
    elif sheet_type == '异常包名应用统计表':
        header_fill = PatternFill(start_color='E74C3C', end_color='E74C3C', fill_type='solid')  # 红色
    elif sheet_type == '异常应用统计报告':
        header_fill = PatternFill(start_color='3498DB', end_color='3498DB', fill_type='solid')  # 蓝色
    else:  # V3.5改进说明
        header_fill = PatternFill(start_color='E67E22', end_color='E67E22', fill_type='solid')  # 橙色

    header_font = Font(color='FFFFFF', bold=True, size=11)
    header_alignment = Alignment(horizontal='center', vertical='center')

    # 设置边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 应用标题行样式
    for cell in worksheet[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # 设置数据行样式
    for row in worksheet.iter_rows(min_row=2):
        for cell in row:
            cell.border = thin_border
            cell.alignment = Alignment(vertical='center', wrap_text=True)

    # 自动调整列宽（V3.4版本，新增异常应用列）
    column_widths = {
        '主数据表': {
            'A': 20,  # 时间
            'B': 35,  # udid
            'C': 80,  # 应用信息
            'D': 60,  # 所有应用名称
            'E': 80,  # 所有包名
            'F': 100, # 应用名称与包名对应关系
            'G': 100, # 异常应用和包名
            'H': 60,  # 异常应用（V3.4新增列）
            'I': 120  # 异常包名原因
        },
        '异常包名应用统计表': {
            'A': 80,  # 应用和异常包名
            'B': 15,  # 出现次数
            'C': 15,  # 占比百分比
            'D': 80   # 检测规则
        },
        '异常应用统计报告': {
            'A': 40,  # 异常应用关键词
            'B': 15,  # 命中次数
            'C': 15   # 占比百分比
        },
        'V3.5改进说明': {
            'A': 30,  # 项目
            'B': 60   # 内容
        }
    }

    widths = column_widths.get(sheet_type, {})
    for column in worksheet.columns:
        column_letter = column[0].column_letter
        width = widths.get(column_letter, 25)
        worksheet.column_dimensions[column_letter].width = width

    # 设置行高
    for row in worksheet.iter_rows():
        worksheet.row_dimensions[row[0].row].height = 35  # 适应内容

def print_analysis_summary_v34(total_records, suspicious_package_records_count, suspicious_app_records_count, package_statistics_data, app_statistics_data, v2_detected, v31_new_detected):
    """打印分析摘要 V3.4"""
    print("\n" + "=" * 80)
    print("📊 屏幕共享数据异常包名分析结果 V3.5")
    print("=" * 80)
    print(f"📈 总记录数: {total_records:,}")
    print(f"🚨 异常包名记录数: {suspicious_package_records_count:,}")
    print(f"🆕 异常应用记录数: {suspicious_app_records_count:,}")
    print(f"📊 异常包名比例: {suspicious_package_records_count/total_records*100:.2f}%")
    print(f"🆕 异常应用比例: {suspicious_app_records_count/total_records*100:.2f}%")
    print(f"🔍 发现异常包名类型: {len(package_statistics_data)} 种")
    print(f"🆕 发现异常应用关键词类型: {len(app_statistics_data)} 种")
    print(f"📊 V2.0规则检测到: {v2_detected} 个异常包名")
    print(f"🔧 V3.1规则检测到: {v31_new_detected} 个异常包名")

    if package_statistics_data:
        print("\n" + "=" * 80)
        print("🏆 异常包名统计排行榜（Top 10）")
        print("=" * 80)
        for i, stat in enumerate(package_statistics_data[:10], 1):
            rule_type = "🔧" if any(pattern in stat['检测规则'] for pattern in ["3字符随机", "字母数字混合", "缺乏可读性", "疑似批量生成", "临时测试", "疑似反编译工具", "无元音字母", "字母数字交替"]) else "📊"
            print(f"{i:2d}. {rule_type} {stat['应用和异常包名']:<50} | {stat['出现次数']:4d} 次 | {stat['占比百分比']:>7s}")

        if len(package_statistics_data) > 10:
            print(f"... 还有 {len(package_statistics_data) - 10} 种其他异常包名")

    if app_statistics_data:
        print("\n" + "=" * 80)
        print("🆕 异常应用关键词统计排行榜")
        print("=" * 80)
        for i, stat in enumerate(app_statistics_data, 1):
            print(f"{i:2d}. 🔍 {stat['异常应用关键词']:<20} | {stat['命中次数']:4d} 次 | {stat['占比百分比']:>7s}")

    print("\n" + "=" * 80)
    print("📋 输出文件说明")
    print("=" * 80)
    print("📊 主数据表 - 包含所有记录的详细分析结果")
    print("  ├─ 时间: 记录时间")
    print("  ├─ udid: 设备唯一标识")
    print("  ├─ 应用信息: 原始应用信息数据")
    print("  ├─ 所有应用名称: 提取的所有应用名称")
    print("  ├─ 所有包名: 提取的所有应用包名")
    print("  ├─ 应用名称与包名对应关系: JSON格式的配对关系")
    print("  ├─ 异常应用和包名: JSON格式的异常应用与包名对应关系")
    print("  ├─ 异常应用: 检测到的异常应用名称（🆕新增）")
    print("  └─ 异常包名原因: 详细的异常判定原因")
    print()
    print("📈 异常包名应用统计表 - 异常包名的统计分析")
    print("🆕 异常应用统计报告 - 异常应用关键词的统计分析（🆕新增）")
    print("🔄 V3.5改进说明 - 修改统计报告表格式")

    print("\n" + "=" * 80)
    print("✨ V3.5 版本改进")
    print("=" * 80)
    print("🔄 重命名\"统计报告表\"为\"异常包名应用统计表\"")
    print("📋 修改\"异常包名\"列为\"应用和异常包名\"列")
    print("📝 输出格式改为\"应用名称：包名\"")
    print("🎯 提供更直观的应用与包名对应关系")
    print("📊 保持异常应用统计报告功能")
    print("🔍 继承所有异常检测功能")

    print("\n" + "=" * 80)
    print("🔍 异常应用检测关键词")
    print("=" * 80)
    print("通信类: 极速通服务、全时通服务、协助通信")
    print("会议类: 微会议、U会议、n会议、天e云")
    print("办公类: 书签、企聊、企达")
    print("通信工具: 信书、信通、公信、云信、达信")
    print("播放器类: AVPlayer、AXVPlayer、AXYPlayer、CDplayer")
    print("特殊匹配: akPlayer/AKPlayer（不区分大小写）")
    print("变体支持: 思欲加速（支持多种分隔符）")
    print("房间类: ROOM")

if __name__ == "__main__":
    # 配置参数
    csv_file = "part-0.csv"
    output_file = "异常包名分析结果_V3.5_修正版.xlsx"

    try:
        print("🎯 屏幕共享数据异常包名分析程序 V3.5")
        print("🔄 修改统计报告表格式，重命名为异常包名应用统计表")
        print("📅 分析时间:", pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"))
        print()

        # 执行分析
        results, package_statistics_data, app_statistics_data, v2_detected, v31_new_detected = analyze_screen_share_data_v34(csv_file, output_file)

        print("\n🎉 V3.5分析完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"🔄 成功修改统计报告表格式")
        print(f"📊 检测效果: V2.0规则 {v2_detected}个 + V3.1规则 {v31_new_detected}个")
        print(f"🆕 异常应用关键词类型: {len(app_statistics_data)}种")

        # 显示异常应用示例
        if results:
            sample_suspicious_app = None
            for result in results:
                if result['异常应用']:
                    sample_suspicious_app = result['异常应用']
                    break

            if sample_suspicious_app:
                print(f"🔍 异常应用示例: {sample_suspicious_app}")

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {csv_file}")
        print("请确保CSV文件存在于当前目录中")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
