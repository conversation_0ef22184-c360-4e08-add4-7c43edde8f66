#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import chardet
import re

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin1']
    
    # 先尝试自动检测编码
    detected_encoding = detect_encoding(file_path)
    if detected_encoding:
        encodings.insert(0, detected_encoding)
    
    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用编码 {encoding} 读取文件")
            return df, encoding
        except Exception as e:
            print(f"编码 {encoding} 失败: {str(e)}")
            continue
    
    raise Exception("无法读取文件，所有编码都失败了")

def extract_package_names(app_info_str):
    """从应用信息字符串中提取包名"""
    if not app_info_str or app_info_str == 'N/A' or app_info_str == '[]':
        return []
    
    package_names = []
    
    print(f"调试包名提取:")
    print(f"  输入数据: {app_info_str[:200]}...")
    print(f"  数据类型: {type(app_info_str)}")
    print(f"  数据长度: {len(app_info_str)}")
    print(f"  开始字符: '{app_info_str[:5]}'")
    print(f"  结束字符: '{app_info_str[-5:]}'")
    
    try:
        # 尝试解析列表格式 [app_name(pkg_name | version), ...]
        if app_info_str.startswith('[') and app_info_str.endswith(']'):
            print("  检测到列表格式")
            # 使用正则表达式提取包名
            # 匹配模式：应用名(包名 | 版本)
            pattern = r'[^(]+\(([a-zA-Z0-9._]+)\s*\|\s*[^)]*\)'
            matches = re.findall(pattern, app_info_str)
            print(f"  正则匹配结果: {len(matches)} 个匹配")
            if matches:
                print(f"  前5个匹配: {matches[:5]}")
            package_names.extend(matches)
            
            # 如果上面的模式没有匹配到，尝试更宽松的模式
            if not matches:
                print("  尝试宽松模式")
                # 匹配所有在括号内、竖线前的内容
                pattern2 = r'\(([^|)]+)\s*\|'
                matches2 = re.findall(pattern2, app_info_str)
                print(f"  宽松模式匹配结果: {len(matches2)} 个匹配")
                if matches2:
                    print(f"  前5个匹配: {matches2[:5]}")
                for match in matches2:
                    # 清理匹配结果，去除空格
                    clean_match = match.strip()
                    if clean_match and '.' in clean_match:  # 包名通常包含点
                        package_names.append(clean_match)
        else:
            print("  不是列表格式")
    
    except Exception as e:
        print(f"解析应用信息失败: {e}")
    
    print(f"  最终提取结果: {len(package_names)} 个包名")
    return package_names

# 读取数据
csv_file = "part-0.csv"
df, encoding = read_csv_with_encoding(csv_file)

print(f"\n数据行数: {len(df)}")
print(f"列名: {list(df.columns)}")

# 查找应用信息列
app_info_column = None
for col in df.columns:
    if '应用信息' in str(col) or 'app' in str(col).lower() and '信息' in str(col):
        app_info_column = col
        break

if app_info_column is None:
    # 查找最后一个非空列名的列（通常是应用信息）
    for col in reversed(df.columns):
        if col and str(col).strip():
            app_info_column = col
            break

print(f"使用应用信息列: {app_info_column}")

# 测试前几行数据
for i in range(min(3, len(df))):
    print(f"\n=== 第{i+1}行数据 ===")
    app_info = str(df.iloc[i][app_info_column]) if pd.notna(df.iloc[i][app_info_column]) else ''
    package_names = extract_package_names(app_info)
    
    # 检查是否包含异常包名
    for pkg in package_names:
        if 'DMey62ZwKlabJ48x' in pkg or 'VSTOgjnDFyx5EZmi' in pkg:
            print(f"*** 发现异常包名: {pkg} ***")
