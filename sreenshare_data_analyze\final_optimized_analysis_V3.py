#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕共享数据异常包名分析程序 V3.0
在V2.0基础上新增5种随机包名检测规则，提高异常包名识别的准确性和覆盖面
"""

import pandas as pd
import re
import json
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import chardet
from collections import Counter
import sys

def detect_encoding(file_path):
    """检测文件编码"""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB用于检测
            result = chardet.detect(raw_data)
            return result['encoding']
    except Exception as e:
        print(f"编码检测失败: {e}")
        return 'utf-8'

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['gb18030', 'gbk', 'gb2312', 'utf-8', 'utf-16', 'latin1']
    
    # 先尝试自动检测编码
    detected_encoding = detect_encoding(file_path)
    if detected_encoding and detected_encoding not in encodings:
        encodings.insert(0, detected_encoding)
    
    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"✅ 成功使用编码 {encoding} 读取文件")
            return df, encoding
        except Exception as e:
            print(f"❌ 编码 {encoding} 失败: {str(e)[:100]}")
            continue
    
    raise Exception("无法读取文件，所有编码都失败了")

def parse_app_info(app_info_str):
    """
    解析应用信息字符串，提取应用名称和包名
    返回: (app_names_list, package_names_list)
    """
    if not app_info_str or app_info_str == 'N/A' or app_info_str.strip() == '[]':
        return [], []
    
    app_info_str = app_info_str.strip()
    app_names = []
    package_names = []
    
    try:
        # 处理JSON格式: {"apps":[{"app_name":"应用名","pkg_name":"包名"}]}
        if app_info_str.startswith('{') and app_info_str.endswith('}'):
            app_data = json.loads(app_info_str)
            if 'apps' in app_data and isinstance(app_data['apps'], list):
                for app in app_data['apps']:
                    if isinstance(app, dict):
                        app_name = app.get('app_name', '')
                        pkg_name = app.get('pkg_name', '')
                        if app_name and pkg_name:
                            app_names.append(app_name)
                            package_names.append(pkg_name)
        
        # 处理列表格式: [应用名(包名 | 版本), ...]
        elif app_info_str.startswith('['):
            # 使用正则表达式匹配: 应用名(包名 | 版本)
            pattern = r'([^(]+)\(([^|)]+)\s*\|\s*[^)]*\)'
            matches = re.findall(pattern, app_info_str)
            
            for app_name, pkg_name in matches:
                app_name = app_name.strip()
                pkg_name = pkg_name.strip()
                if app_name and pkg_name and '.' in pkg_name:  # 包名通常包含点
                    app_names.append(app_name)
                    package_names.append(pkg_name)
            
            # 如果上面的模式没有匹配到，尝试更宽松的模式
            if not matches:
                pattern2 = r'([^(]+)\(([^|)]+)\s*\|'
                matches2 = re.findall(pattern2, app_info_str)
                for app_name, pkg_name in matches2:
                    app_name = app_name.strip()
                    pkg_name = pkg_name.strip()
                    if app_name and pkg_name and '.' in pkg_name:
                        app_names.append(app_name)
                        package_names.append(pkg_name)
    
    except Exception as e:
        print(f"解析应用信息失败: {e}")
        print(f"原始数据: {app_info_str[:200]}...")
    
    return app_names, package_names

def is_suspicious_package_name_v3(pkg_name):
    """
    判断是否为异常包名 V3.0
    在V2.0基础上新增5种随机包名检测规则
    """
    if not pkg_name or pkg_name == 'N/A':
        return False, ""
    
    # 已知的正常包名白名单（避免误判）
    normal_patterns = [
        r'^com\.android\..*',  # Android系统包名
        r'^com\.google\..*',   # Google官方应用
        r'^com\.samsung\..*',  # 三星官方应用
        r'^com\.huawei\..*',   # 华为官方应用
        r'^com\.xiaomi\..*',   # 小米官方应用
        r'^com\.oppo\..*',     # OPPO官方应用
        r'^com\.vivo\..*',     # VIVO官方应用
        r'^com\.tencent\..*',  # 腾讯系列应用
        r'^com\.alibaba\..*',  # 阿里系列应用
        r'^com\.baidu\..*',    # 百度系列应用
        r'^cn\..*',            # 中国域名包名
        r'^org\..*',           # 组织域名包名
        r'^net\..*',           # 网络域名包名
        r'^edu\..*',           # 教育域名包名
        r'^gov\..*',           # 政府域名包名
        # V3.0新增白名单 - 扩展常见正常应用
        r'^com\.microsoft\..*', # 微软官方应用
        r'^com\.apple\..*',     # 苹果官方应用
        r'^com\.facebook\..*',  # Facebook系列应用
        r'^com\.twitter\..*',   # Twitter官方应用
        r'^com\.instagram\..*', # Instagram官方应用
        r'^com\.whatsapp\..*',  # WhatsApp官方应用
        r'^com\.linkedin\..*',  # LinkedIn官方应用
        r'^com\.netflix\..*',   # Netflix官方应用
        r'^com\.spotify\..*',   # Spotify官方应用
        r'^com\.adobe\..*',     # Adobe系列应用
        # 常见的正常应用包名
        r'^com\.icbc$',         # 工商银行
        r'^com\.unionpay$',     # 银联
        r'^com\.wuba$',         # 58同城
        r'^com\.videogo$',      # 萤石云视频
        r'^com\.paem$',         # 平安证券
        r'^com\.htinns$',       # 华住会
        r'^com\.mosheng$',      # 陌生人
        r'^com\.nbbank$',       # 宁波银行
        r'^com\.forms$',        # 表单应用
        r'^com\.yipiao$',       # 易票
        r'^com\.yoosee$',       # 有看头
        r'^com\.wlqq$',         # 未来QQ
        r'^com\.jin10$',        # 金十数据
        r'^com\.mymoney$',      # 随手记
        r'^com\.szzc$',         # 深圳证券
        r'^com\.ehai$',         # 易海
        r'^com\.zxscnew$',      # 中信证券
        r'^com\.booking$',      # 缤客
        r'^com\.changba$',      # 唱吧
        r'^com\.jxedt$',        # 驾校一点通
        # 其他常见的正常包名模式
        r'^com\.[a-z]{4,}$',    # 4个字符以上的单词通常是正常的
    ]
    
    # 检查是否在白名单中
    for pattern in normal_patterns:
        if re.match(pattern, pkg_name):
            return False, ""
    
    # V2.0原有的异常模式检测
    v2_suspicious_patterns = [
        # 1. 精确匹配已知的恶意包名
        (r'^com\.DMey62ZwKlabJ48x\.VSTOgjnDFyx5EZmi$', "已知恶意随机包名"),
        
        # 2. 明显的随机字符串模式
        (r'[A-Z][a-z]{3}\d{2}[A-Z][a-z]{2}[A-Z][a-z]{3}[A-Z]\d{2}[a-z]', "包含类似DMey62ZwKlabJ48x的随机模式"),
        (r'[A-Z]{3}[a-z]{4}[A-Z][a-z]{4}[A-Z][a-z]{2}[A-Z][a-z]{2}', "包含类似VSTOgjnDFyx5EZmi的随机模式"),
        
        # 3. 包含时间戳的包名（10位数字，通常是Unix时间戳）
        (r'\.hd\d{10}$', "包含时间戳标识的包名"),
        (r'\.lt\d{10}$', "包含时间戳标识的包名"),
        (r'\.m\d{10}$', "包含时间戳标识的包名"),
        (r'com\.a\d{10}$', "包含时间戳的简单随机包名"),
        (r'com\.tiktok\.dy\d{8}$', "TikTok相关的时间戳包名"),
        
        # 4. 第三方平台生成的随机包名
        (r'org\.zywx\.wbpalmstar\.widgetone\.uex\d{8,}$', "正益无线平台生成的随机包名"),
        (r'com\.apicloud\.[A-Z]\d{8,}$', "APICloud平台生成的随机包名"),
        
        # 5. 包含大量数字的简单随机包名
        (r'com\.g\d{8,}\.[a-z]{1,5}$', "包含大量数字的简单随机包名"),
        (r'com\.network\.xf\d{8,}$', "网络相关的随机包名"),
        
        # 6. 复杂的多段随机字符串
        (r'com\.ndc[a-z0-9]{6,}\.nec[a-z0-9]{6,}\.jeb[a-z0-9]{6,}\.nd[a-z0-9]{10,}', "包含多段随机字符串的复杂包名"),
        
        # 7. 包含明显随机字符串段的包名
        (r'\.[A-Z][a-z]+\d{3,}[A-Z][a-z]+[A-Z]\d+[a-z]', "包含随机字符串段"),
        (r'\.[A-Z]{3,}[a-z]{4,}[A-Z][a-z]{4,}[A-Z][a-z]{2,}[A-Z][a-z]{2,}', "包含复杂随机字符串段"),
        
        # 8. 字节跳动小程序相关的随机包名
        (r'com\.bytedance\.tt[a-z0-9]{16,}\.miniapk$', "字节跳动小程序随机包名"),
        
        # 9. 包含明显拼写错误或随机组合的英文单词
        (r'com\.[a-z]+\.(conditionerremotecontrold|mobilephonelocationtracking|mobilephonepositioningartifact)$', "包含拼写错误的英文单词"),
        (r'com\.saoyisaoruanjiansaosaogengjiankang$', "包含超长中文拼音组合"),
        
        # 10. 其他明显的随机模式
        (r'com\.fallturgggye\.spentyyrtudid\.fjvghbbhallturf$', "明显的随机字符串组合"),
        (r'com\.mo3ph0o\.m\d{10}$', "包含随机字符和时间戳的包名"),
    ]
    
    # V3.0新增的异常模式检测（更精确的规则）
    v3_new_patterns = [
        # 1. 无固定规则的随机组合包名（排除常见单词）
        (r'^com\.[a-z0-9]{3}$', "系统随机生成的3字符简单包名"),
        (r'^com\.[a-z]{1,2}[0-9]{1,2}[a-z]{1,2}$', "字母数字混合的随机包名"),

        # 2. 无逻辑结构的随机包名
        (r'^com\.[a-z]{2,3}[0-9]{1,2}[a-z]{1,2}\.(app|test|tmp)$', "缺乏可读性的无逻辑结构包名"),

        # 3. 批量生成的马甲包名（更严格的模式）
        (r'^com\.[a-z]{1,2}[0-9]{4,6}[a-z]{0,2}$', "疑似批量生成的马甲包名"),

        # 4. 临时测试包名
        (r'^com\.(test|tmp|temp|demo)[a-z0-9]{2,8}$', "临时测试或分身应用包名"),

        # 5. 反编译工具生成的随机包名
        (r'^com\.[a-z]{1,2}[0-9]{2,4}[a-z]{1,2}\.[a-z]{1,3}[0-9]{1,3}$', "疑似反编译工具随机替换的包名"),

        # 6. 明显的随机字符组合（新增）
        (r'^com\.[bcdfghjklmnpqrstvwxyz]{3,5}$', "无元音字母的随机包名"),
        (r'^com\.[a-z]{2}[0-9][a-z][0-9]$', "字母数字交替的随机包名"),
    ]
    
    # 合并所有检测模式
    all_patterns = v2_suspicious_patterns + v3_new_patterns
    
    # 检查每个可疑模式
    for pattern, reason in all_patterns:
        if re.search(pattern, pkg_name):
            return True, reason
    
    return False, ""

def analyze_screen_share_data_v3(csv_file_path, output_excel_path):
    """
    分析屏幕共享数据 V3.0
    """
    print("🚀 开始屏幕共享数据异常包名分析 V3.0")
    print("🆕 新增5种随机包名检测规则，提高识别准确性和覆盖面")
    print("=" * 70)

    # 1. 读取CSV文件
    print("📖 正在读取CSV文件...")
    df, encoding = read_csv_with_encoding(csv_file_path)
    print(f"✅ 数据读取完成，共 {len(df):,} 行数据")
    print(f"📊 数据列数: {len(df.columns)}")

    # 2. 查找应用信息列
    print("\n🔍 正在查找应用信息列...")
    app_info_column = None
    for col in df.columns:
        if '应用信息' in str(col):
            app_info_column = col
            break

    if app_info_column is None:
        # 查找最后一个非空列名的列（通常是应用信息）
        for col in reversed(df.columns):
            if col and str(col).strip():
                app_info_column = col
                break

    if app_info_column is None:
        raise Exception("未找到应用信息列")

    print(f"✅ 找到应用信息列: {app_info_column}")

    # 3. 解析应用信息并检测异常包名
    print("\n🔬 正在解析应用信息并检测异常包名...")
    print("🆕 使用V3.0增强检测规则...")

    results = []
    all_suspicious_packages = []
    processed_count = 0
    v2_detected = 0  # V2.0规则检测到的数量
    v3_new_detected = 0  # V3.0新规则检测到的数量

    for index, row in df.iterrows():
        processed_count += 1

        # 显示进度
        if processed_count % 1000 == 0:
            print(f"⏳ 已处理 {processed_count:,}/{len(df):,} 行数据 ({processed_count/len(df)*100:.1f}%)")

        # 获取应用信息
        app_info = str(row[app_info_column]) if pd.notna(row[app_info_column]) else ''

        # 解析应用信息
        app_names, package_names = parse_app_info(app_info)

        # 检测异常包名
        suspicious_packages = []
        suspicious_reasons = []

        for pkg_name in package_names:
            is_suspicious, reason = is_suspicious_package_name_v3(pkg_name)
            if is_suspicious:
                suspicious_packages.append(pkg_name)
                suspicious_reasons.append(f"{pkg_name}:{reason}")
                all_suspicious_packages.append(pkg_name)

                # 统计V2.0和V3.0新规则的检测效果
                if any(pattern in reason for pattern in ["系统随机生成", "缺乏可读性", "疑似批量生成", "临时测试", "疑似反编译工具"]):
                    v3_new_detected += 1
                else:
                    v2_detected += 1

        # 构建结果记录
        result_record = {
            '时间': row.get('时间', ''),
            'udid': row.get('udid', ''),
            '应用信息': app_info,
            '所有应用名称': ', '.join(app_names) if app_names else '',
            '所有包名': ', '.join(package_names) if package_names else '',
            '异常包名': ', '.join(suspicious_packages) if suspicious_packages else '',
            '异常包名原因': '; '.join(suspicious_reasons) if suspicious_reasons else ''
        }

        results.append(result_record)

    print(f"✅ 数据解析完成，共处理 {len(results):,} 行数据")
    print(f"📊 V2.0规则检测到: {v2_detected} 个异常包名")
    print(f"🆕 V3.0新规则检测到: {v3_new_detected} 个异常包名")

    # 4. 创建统计数据
    print("\n📊 正在生成统计数据...")
    package_counter = Counter(all_suspicious_packages)
    total_suspicious_count = len(all_suspicious_packages)

    statistics_data = []
    for package, count in package_counter.most_common():
        percentage = (count / total_suspicious_count) * 100 if total_suspicious_count > 0 else 0

        # 获取包名的检测原因
        _, reason = is_suspicious_package_name_v3(package)

        statistics_data.append({
            '异常包名': package,
            '出现次数': count,
            '占比百分比': f"{percentage:.2f}%",
            '检测规则': reason
        })

    print(f"✅ 统计数据生成完成")
    print(f"📈 发现异常包名类型: {len(statistics_data)} 种")
    print(f"📈 异常包名总出现次数: {total_suspicious_count:,} 次")

    # 5. 生成Excel文件
    print(f"\n📝 正在生成Excel文件: {output_excel_path}")
    create_excel_report_v3(results, statistics_data, output_excel_path, v2_detected, v3_new_detected)

    # 6. 打印分析摘要
    print_analysis_summary_v3(len(df), len([r for r in results if r['异常包名']]), statistics_data, v2_detected, v3_new_detected)

    return results, statistics_data, v2_detected, v3_new_detected

def create_excel_report_v3(results, statistics_data, output_excel_path, v2_detected, v3_new_detected):
    """创建Excel报告 V3.0"""
    try:
        # 创建DataFrame
        main_df = pd.DataFrame(results)
        stats_df = pd.DataFrame(statistics_data)

        # 保存到Excel
        with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
            # 写入主数据
            main_df.to_excel(writer, sheet_name='主数据表', index=False)

            # 写入统计数据
            if not stats_df.empty:
                stats_df.to_excel(writer, sheet_name='统计报告表', index=False)

            # 创建V3.0改进说明工作表
            create_v3_improvement_sheet(writer, v2_detected, v3_new_detected, len(statistics_data))

            # 设置样式
            workbook = writer.book

            # 设置主数据表样式
            main_worksheet = writer.sheets['主数据表']
            set_worksheet_style_v3(main_worksheet, '主数据表')

            # 设置统计报告表样式
            if not stats_df.empty:
                stats_worksheet = writer.sheets['统计报告表']
                set_worksheet_style_v3(stats_worksheet, '统计报告表')

            # 设置改进说明表样式
            improvement_worksheet = writer.sheets['V3.0改进说明']
            set_worksheet_style_v3(improvement_worksheet, 'V3.0改进说明')

        print(f"✅ Excel文件生成成功: {output_excel_path}")

    except Exception as e:
        print(f"❌ Excel文件生成失败: {e}")
        raise

def create_v3_improvement_sheet(writer, v2_detected, v3_new_detected, total_types):
    """创建V3.0改进说明工作表"""
    improvement_data = [
        ['V3.0版本改进说明', ''],
        ['', ''],
        ['改进项目', '说明'],
        ['新增检测规则数量', '5种'],
        ['V2.0规则检测到的异常包名', f'{v2_detected}个'],
        ['V3.0新规则检测到的异常包名', f'{v3_new_detected}个'],
        ['总异常包名类型', f'{total_types}种'],
        ['检测覆盖面提升', f'{((v2_detected + v3_new_detected) / max(v2_detected, 1) - 1) * 100:.1f}%'],
        ['', ''],
        ['新增检测规则详情', ''],
        ['', ''],
        ['规则1', '无固定规则的随机组合包名'],
        ['检测模式', 'com.[a-z0-9]{3,8}$'],
        ['示例', 'com.a3b5c7'],
        ['', ''],
        ['规则2', '无逻辑结构的随机包名'],
        ['检测模式', 'com.[a-z]{2,4}[0-9]{1,3}[a-z]{1,3}.(app|test|tmp)$'],
        ['示例', 'com.xk9j3f.app'],
        ['', ''],
        ['规则3', '批量生成的马甲包名'],
        ['检测模式', 'com.[a-z]{1,3}[0-9]{3,6}[a-z]{0,3}$'],
        ['示例', 'com.ab123cd'],
        ['', ''],
        ['规则4', '临时测试包名'],
        ['检测模式', 'com.(test|tmp|temp|demo)[a-z0-9]{2,8}$'],
        ['示例', 'com.test123'],
        ['', ''],
        ['规则5', '反编译工具生成的随机包名'],
        ['检测模式', 'com.[a-z]{1,2}[0-9]{2,4}[a-z]{1,2}.[a-z]{1,3}[0-9]{1,3}$'],
        ['示例', 'com.a12b.c3d'],
    ]

    improvement_df = pd.DataFrame(improvement_data, columns=['项目', '内容'])
    improvement_df.to_excel(writer, sheet_name='V3.0改进说明', index=False)

def set_worksheet_style_v3(worksheet, sheet_type):
    """设置工作表样式 V3.0"""
    # 设置标题行样式
    if sheet_type == '主数据表':
        header_fill = PatternFill(start_color='2C3E50', end_color='2C3E50', fill_type='solid')  # 深蓝色
    elif sheet_type == '统计报告表':
        header_fill = PatternFill(start_color='E74C3C', end_color='E74C3C', fill_type='solid')  # 红色
    else:  # V3.0改进说明
        header_fill = PatternFill(start_color='27AE60', end_color='27AE60', fill_type='solid')  # 绿色

    header_font = Font(color='FFFFFF', bold=True, size=11)
    header_alignment = Alignment(horizontal='center', vertical='center')

    # 设置边框
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # 应用标题行样式
    for cell in worksheet[1]:
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = header_alignment
        cell.border = thin_border

    # 设置数据行样式
    for row in worksheet.iter_rows(min_row=2):
        for cell in row:
            cell.border = thin_border
            cell.alignment = Alignment(vertical='center', wrap_text=True)

    # 自动调整列宽
    column_widths = {
        '主数据表': {
            'A': 20,  # 时间
            'B': 35,  # udid
            'C': 80,  # 应用信息
            'D': 60,  # 所有应用名称
            'E': 80,  # 所有包名
            'F': 50,  # 异常包名
            'G': 120  # 异常包名原因
        },
        '统计报告表': {
            'A': 60,  # 异常包名
            'B': 15,  # 出现次数
            'C': 15,  # 占比百分比
            'D': 80   # 检测规则
        },
        'V3.0改进说明': {
            'A': 30,  # 项目
            'B': 60   # 内容
        }
    }

    widths = column_widths.get(sheet_type, {})
    for column in worksheet.columns:
        column_letter = column[0].column_letter
        width = widths.get(column_letter, 25)
        worksheet.column_dimensions[column_letter].width = width

    # 设置行高
    for row in worksheet.iter_rows():
        worksheet.row_dimensions[row[0].row].height = 30

def print_analysis_summary_v3(total_records, suspicious_records_count, statistics_data, v2_detected, v3_new_detected):
    """打印分析摘要 V3.0"""
    print("\n" + "=" * 80)
    print("📊 屏幕共享数据异常包名分析结果 V3.0")
    print("=" * 80)
    print(f"📈 总记录数: {total_records:,}")
    print(f"🚨 异常记录数: {suspicious_records_count:,}")
    print(f"📊 异常比例: {suspicious_records_count/total_records*100:.2f}%")
    print(f"🔍 发现异常包名类型: {len(statistics_data)} 种")
    print(f"📊 V2.0规则检测到: {v2_detected} 个异常包名")
    print(f"🆕 V3.0新规则检测到: {v3_new_detected} 个异常包名")
    print(f"📈 检测覆盖面提升: {((v2_detected + v3_new_detected) / max(v2_detected, 1) - 1) * 100:.1f}%")

    if statistics_data:
        print("\n" + "=" * 80)
        print("🏆 异常包名统计排行榜（Top 20）")
        print("=" * 80)
        for i, stat in enumerate(statistics_data[:20], 1):
            rule_type = "🆕" if any(pattern in stat['检测规则'] for pattern in ["系统随机生成", "缺乏可读性", "疑似批量生成", "临时测试", "疑似反编译工具"]) else "📊"
            print(f"{i:2d}. {rule_type} {stat['异常包名']:<50} | {stat['出现次数']:4d} 次 | {stat['占比百分比']:>7s}")

        if len(statistics_data) > 20:
            print(f"... 还有 {len(statistics_data) - 20} 种其他异常包名")

        # 按检测规则分类统计
        print("\n" + "=" * 80)
        print("📋 V3.0新增规则检测效果")
        print("=" * 80)
        v3_rules_stats = {}
        for stat in statistics_data:
            rule = stat['检测规则']
            if any(pattern in rule for pattern in ["系统随机生成", "缺乏可读性", "疑似批量生成", "临时测试", "疑似反编译工具"]):
                if rule not in v3_rules_stats:
                    v3_rules_stats[rule] = 0
                v3_rules_stats[rule] += stat['出现次数']

        for rule, count in sorted(v3_rules_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"🆕 {rule}: {count} 个")

    print("\n" + "=" * 80)
    print("📋 输出文件说明")
    print("=" * 80)
    print("📊 主数据表 - 包含所有记录的详细分析结果")
    print("  ├─ 时间: 记录时间")
    print("  ├─ udid: 设备唯一标识")
    print("  ├─ 应用信息: 原始应用信息数据")
    print("  ├─ 所有应用名称: 提取的所有应用名称")
    print("  ├─ 所有包名: 提取的所有应用包名")
    print("  ├─ 异常包名: 检测到的异常包名")
    print("  └─ 异常包名原因: 详细的异常判定原因")
    print()
    print("📈 统计报告表 - 异常包名的统计分析")
    print("  ├─ 异常包名: 具体的异常包名")
    print("  ├─ 出现次数: 在所有记录中的出现次数")
    print("  ├─ 占比百分比: 在所有异常包名中的占比")
    print("  └─ 检测规则: 具体的检测规则说明")
    print()
    print("🆕 V3.0改进说明 - 新增功能和改进效果")
    print("  ├─ 版本对比数据")
    print("  ├─ 新增检测规则详情")
    print("  └─ 检测模式和示例")

    print("\n" + "=" * 80)
    print("✨ V3.0 版本改进")
    print("=" * 80)
    print("🎯 新增5种随机包名检测规则")
    print("🔍 提高异常包名识别的准确性和覆盖面")
    print("📊 增强统计报告，包含检测规则分类")
    print("📋 新增V3.0改进说明工作表")
    print("🆕 区分V2.0和V3.0规则的检测效果")
    print("📈 提供详细的版本对比分析")

if __name__ == "__main__":
    # 配置参数
    csv_file = "part-0.csv"
    output_file = "异常包名分析结果_V3.xlsx"

    try:
        print("🎯 屏幕共享数据异常包名分析程序 V3.0")
        print("🆕 新增5种随机包名检测规则，提高识别准确性和覆盖面")
        print("📅 分析时间:", pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"))
        print()

        # 执行分析
        results, statistics_data, v2_detected, v3_new_detected = analyze_screen_share_data_v3(csv_file, output_file)

        print("\n🎉 V3.0分析完成！")
        print(f"📁 输出文件: {output_file}")
        print(f"🆕 V3.0新规则贡献: {v3_new_detected}/{v2_detected + v3_new_detected} ({v3_new_detected/(v2_detected + v3_new_detected)*100:.1f}%)")

    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {csv_file}")
        print("请确保CSV文件存在于当前目录中")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
