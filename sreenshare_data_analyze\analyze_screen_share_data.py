#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
屏幕共享数据分析脚本
功能：过滤出包含异常包名（随机包名）的数据并输出到Excel
"""

import pandas as pd
import re
import json
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import chardet

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin1']
    
    # 先尝试自动检测编码
    detected_encoding = detect_encoding(file_path)
    if detected_encoding:
        encodings.insert(0, detected_encoding)
    
    for encoding in encodings:
        try:
            print(f"尝试使用编码: {encoding}")
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用编码 {encoding} 读取文件")
            return df, encoding
        except Exception as e:
            print(f"编码 {encoding} 失败: {str(e)}")
            continue
    
    raise Exception("无法读取文件，所有编码都失败了")

def is_suspicious_package_name(pkg_name):
    """
    判断是否为可疑的随机包名
    特征：
    1. 包含随机字符串
    2. 不符合常见包名规范
    3. 包含特殊字符组合
    """
    if not pkg_name or pkg_name == 'N/A':
        return False

    # 已知的正常包名白名单（避免误判）
    whitelist_patterns = [
        r'com\.greenpoint\.android\.mc10086\.activity',  # 中国移动官方应用
        r'com\.sankuai\.meituan\..*',  # 美团系列应用
        r'com\.shusheng\.JoJoRead',  # 正常的阅读应用
        r'com\..*\.activity$',  # 以activity结尾的通常是正常的
        r'com\..*\.android\..*',  # 包含android的通常是正常的
    ]

    # 检查是否在白名单中
    for pattern in whitelist_patterns:
        if re.match(pattern, pkg_name):
            return False

    # 特殊的随机字符串检测（针对具体的异常包名模式）
    # 如：com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi
    random_like_patterns = [
        r'[A-Z][a-z]+\d+[A-Z][a-z]+[A-Z]\d+[a-z]',  # 类似 DMey62ZwKlabJ48x 的模式
        r'[A-Z]{2,}[a-z]+[A-Z]{2,}[a-z]+[A-Z]',     # 类似 VSTOgjnDFyx5EZmi 的模式
        r'[A-Z][a-z]{2,}\d{2,}[A-Z][a-z]{2,}[A-Z]\d+[a-z]',  # 更精确的随机模式
        r'[A-Z]{1}[a-z]{3}\d{2}[A-Z]{1}[a-z]{2}[A-Z]{1}[a-z]{3}[A-Z]{1}\d{2}[a-z]{1}',  # DMey62ZwKlabJ48x 模式
        r'[A-Z]{3}[a-z]{4}[A-Z]{1}[a-z]{4}[A-Z]{1}[a-z]{2}[A-Z]{1}[a-z]{2}',  # VSTOgjnDFyx5EZmi 模式
    ]

    has_random_pattern = any(re.search(pattern, pkg_name) for pattern in random_like_patterns)

    # 更严格的可疑特征检查
    suspicious_patterns = [
        # 包含明显的随机字符串段
        r'\.[A-Z][a-z]+\d+[A-Z][a-z]+[A-Z]\d+[a-z]',  # 如 .DMey62ZwKlabJ48x
        r'\.[A-Z]{3,}[a-z]+[A-Z]{2,}[a-z]+[A-Z][a-z]+',  # 如 .VSTOgjnDFyx5EZmi
        # 包含超长的随机字符串
        r'[a-zA-Z]{25,}',  # 超长的字符串（25个字符以上）
        # 包含大量数字的随机模式
        r'\d{6,}',  # 包含6位以上连续数字
    ]

    # 检查可疑模式
    has_suspicious_pattern = any(re.search(pattern, pkg_name) for pattern in suspicious_patterns)

    # 判断逻辑：
    # 1. 如果有明显的随机模式，则认为是可疑的
    # 2. 如果有可疑特征，也认为是可疑的
    return has_random_pattern or has_suspicious_pattern

def extract_package_names(app_info_str):
    """从应用信息字符串中提取包名"""
    if not app_info_str or app_info_str == 'N/A' or app_info_str == '[]':
        return []

    # 清理数据，去除首尾空格
    app_info_str = app_info_str.strip()

    package_names = []

    try:
        # 尝试解析JSON格式
        if app_info_str.startswith('{') and app_info_str.endswith('}'):
            app_data = json.loads(app_info_str)
            if 'apps' in app_data:
                for app in app_data['apps']:
                    if 'pkg_name' in app:
                        package_names.append(app['pkg_name'])

        # 尝试解析列表格式 [app_name(pkg_name | version), ...]
        elif app_info_str.startswith('['):
            # 使用正则表达式提取包名
            # 匹配模式：应用名(包名 | 版本)
            pattern = r'[^(]+\(([a-zA-Z0-9._]+)\s*\|\s*[^)]*\)'
            matches = re.findall(pattern, app_info_str)
            package_names.extend(matches)

            # 如果上面的模式没有匹配到，尝试更宽松的模式
            if not matches:
                # 匹配所有在括号内、竖线前的内容
                pattern2 = r'\(([^|)]+)\s*\|'
                matches2 = re.findall(pattern2, app_info_str)
                for match in matches2:
                    # 清理匹配结果，去除空格
                    clean_match = match.strip()
                    if clean_match and '.' in clean_match:  # 包名通常包含点
                        package_names.append(clean_match)

    except Exception as e:
        print(f"解析应用信息失败: {e}")
        print(f"原始数据: {app_info_str[:100]}...")

    return package_names

def analyze_screen_share_data(csv_file_path, output_excel_path):
    """分析屏幕共享数据并输出异常包名到Excel"""
    
    print("开始分析屏幕共享数据...")
    
    # 读取CSV文件
    df, encoding = read_csv_with_encoding(csv_file_path)
    print(f"数据行数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    
    # 查找包含应用信息的列
    app_info_column = None
    for col in df.columns:
        if '应用信息' in str(col) or 'app' in str(col).lower() and '信息' in str(col):
            app_info_column = col
            break

    if app_info_column is None:
        # 查找最后一个非空列名的列（通常是应用信息）
        for col in reversed(df.columns):
            if col and str(col).strip():
                app_info_column = col
                break

    print(f"使用应用信息列: {app_info_column}")

    # 显示前几行数据以便调试
    print("\n=== 数据样本 ===")
    for i in range(min(3, len(df))):
        app_info = str(df.iloc[i][app_info_column]) if pd.notna(df.iloc[i][app_info_column]) else ''
        print(f"第{i+1}行应用信息: {app_info[:200]}...")
        if len(app_info) > 200:
            print("  (数据已截断)")
        print()
    
    # 分析数据
    suspicious_records = []
    debug_count = 0

    for index, row in df.iterrows():
        app_info = str(row[app_info_column]) if pd.notna(row[app_info_column]) else ''

        # 提取包名
        package_names = extract_package_names(app_info)

        # 调试：显示前几行的包名提取结果
        if debug_count < 5:
            print(f"\n=== 调试信息 第{index+1}行 ===")
            print(f"原始应用信息: {app_info[:300]}...")
            print(f"提取到的包名数量: {len(package_names)}")
            if package_names:
                print(f"前5个包名: {package_names[:5]}")
                # 检查是否包含异常包名
                for pkg in package_names[:10]:  # 检查前10个包名
                    if 'DMey62ZwKlabJ48x' in pkg or 'VSTOgjnDFyx5EZmi' in pkg:
                        print(f"  发现目标异常包名: {pkg}")
            debug_count += 1

        # 检查是否有可疑包名
        suspicious_packages = []
        for pkg_name in package_names:
            if is_suspicious_package_name(pkg_name):
                suspicious_packages.append(pkg_name)
                print(f"发现可疑包名: {pkg_name}")

        if suspicious_packages:
            record = row.to_dict()
            record['可疑包名'] = ', '.join(suspicious_packages)
            record['所有包名'] = ', '.join(package_names)
            suspicious_records.append(record)
    
    print(f"发现 {len(suspicious_records)} 条包含异常包名的记录")
    
    # 创建结果DataFrame
    if suspicious_records:
        result_df = pd.DataFrame(suspicious_records)
        
        # 保存到Excel
        with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
            result_df.to_excel(writer, sheet_name='异常包名数据', index=False)
            
            # 获取工作表并设置样式
            worksheet = writer.sheets['异常包名数据']
            
            # 设置标题行样式
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            header_font = Font(color='FFFFFF', bold=True)
            
            for cell in worksheet[1]:
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = Alignment(horizontal='center')
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"结果已保存到: {output_excel_path}")
        
        # 显示一些统计信息
        print("\n=== 分析结果统计 ===")
        print(f"总记录数: {len(df)}")
        print(f"异常记录数: {len(suspicious_records)}")
        print(f"异常比例: {len(suspicious_records)/len(df)*100:.2f}%")
        
        # 显示前几个异常包名示例
        print("\n=== 异常包名示例 ===")
        for i, record in enumerate(suspicious_records[:5]):
            print(f"{i+1}. {record['可疑包名']}")
    
    else:
        print("未发现异常包名")
    
    return suspicious_records

if __name__ == "__main__":
    csv_file = "part-0.csv"
    output_file = "异常包名分析结果.xlsx"
    
    try:
        results = analyze_screen_share_data(csv_file, output_file)
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
