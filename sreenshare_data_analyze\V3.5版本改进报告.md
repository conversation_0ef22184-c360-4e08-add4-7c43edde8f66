# 屏幕共享数据异常包名分析报告 V3.5

## 🎯 项目概述

基于用户要求，在V3.4版本基础上对统计报告表进行格式改进。V3.5版本将"统计报告表"重命名为"异常包名应用统计表"，并将其中的"异常包名"列改为"应用和异常包名"列，采用"应用名称：包名"的格式，提供更直观的应用与包名对应关系显示。

## ✨ V3.5版本主要改进

### 🔄 核心改进

#### 1. **工作表重命名**
- **原名称**: 统计报告表
- **新名称**: 异常包名应用统计表
- **改进目的**: 更准确地反映工作表内容，突出应用与包名的对应关系

#### 2. **列名修改**
- **原列名**: 异常包名
- **新列名**: 应用和异常包名
- **改进目的**: 明确显示应用名称与包名的对应关系

#### 3. **输出格式改进**
- **原格式**: 仅显示包名
- **新格式**: "应用名称：包名"
- **示例**: 
  - `黑洞加速器：com.blackhole.hd1750243203`
  - `华彩人生：com.hcrs`
  - `掌上有色：com.smm`

### 🔧 技术实现改进

#### 1. **反向映射增强**
```python
# 创建包名到应用名称的反向映射（用于统计表）
pkg_to_app_mapping_for_stats = {}
for result in results:
    if result['异常应用和包名'] and result['异常应用和包名'] != '{}':
        try:
            suspicious_mapping = json.loads(result['异常应用和包名'])
            for app_name, pkg_name in suspicious_mapping.items():
                if pkg_name not in pkg_to_app_mapping_for_stats:
                    pkg_to_app_mapping_for_stats[pkg_name] = []
                if app_name not in pkg_to_app_mapping_for_stats[pkg_name]:
                    pkg_to_app_mapping_for_stats[pkg_name].append(app_name)
        except:
            continue
```

#### 2. **格式化输出优化**
```python
# 格式化应用和异常包名
if package in pkg_to_app_mapping_for_stats:
    app_names = pkg_to_app_mapping_for_stats[package]
    app_package_format = f"{app_names[0]}：{package}"  # 取第一个应用名称
else:
    app_package_format = f"未知应用：{package}"
```

#### 3. **Excel样式调整**
- **列宽优化**: A列（应用和异常包名列）从60像素调整为80像素
- **工作表颜色**: 保持红色标题行，与其他工作表区分
- **内容显示**: 支持中文冒号分隔符的正确显示

## 📊 分析结果

### 数据概览
- **数据源文件**: `part-0.csv`
- **总记录数**: 9,500条
- **异常包名记录数**: 305条（3.21%）
- **异常应用记录数**: 225条（2.37%）
- **发现异常包名类型**: 23种
- **发现异常应用关键词类型**: 3种

### 📈 检测效果统计
- **V2.0规则检测**: 147个异常包名
- **V3.1规则检测**: 163个异常包名
- **异常应用检测**: 225个异常应用
- **异常应用关键词**: 3种类型

### 🏆 异常包名应用统计排行榜（Top 10）

| 排名 | 应用和异常包名 | 出现次数 | 占比 | 检测规则 |
|------|----------------|----------|------|----------|
| 1 | 黑洞加速器：com.blackhole.hd1750243203 | 76次 | 24.52% | 📊 V2.0规则 |
| 2 | 华彩人生：com.hcrs | 63次 | 20.32% | 🔧 V3.1规则 |
| 3 | 掌上有色：com.smm | 34次 | 10.97% | 🔧 V3.1规则 |
| 4 | 鲁班到家师傅版：com.jdsq | 29次 | 9.35% | 🔧 V3.1规则 |
| 5 | 面椇宮社：com.fallturgggye.spentyyrtudid.fjvghbbhallturf | 21次 | 6.77% | 📊 V2.0规则 |
| 6 | 政务助手：com.zwzs | 16次 | 5.16% | 🔧 V3.1规则 |
| 7 | 民泰银行：com.mtb | 14次 | 4.52% | 🔧 V3.1规则 |
| 8 | 天天狙击2：com.bytedance.tt08107ead972e1dd807.miniapk | 14次 | 4.52% | 📊 V2.0规则 |
| 9 | JJ斗地主：com.bytedance.tt3bea4d57a4d1e47d.miniapk | 12次 | 3.87% | 📊 V2.0规则 |
| 10 | 茄子视频：com.ndc1b4ee01.nec5f215b8.jeb49be4933.nd21f1af8720241119 | 10次 | 3.23% | 📊 V2.0规则 |

### 🆕 异常应用关键词统计排行榜

| 排名 | 异常应用关键词 | 命中次数 | 占比 |
|------|----------------|----------|------|
| 1 | 思欲加速 | 3次 | 1.33% |
| 2 | 信通 | 2次 | 0.89% |
| 3 | 达信 | 1次 | 0.44% |

## 📁 输出文件结构

### Excel文件：`异常包名分析结果_V3.5_修正版.xlsx`

#### 工作表1：主数据表
包含9,500条完整记录，每条记录包含以下9列：
- **时间**: 记录时间
- **udid**: 设备唯一标识
- **应用信息**: 原始应用信息数据
- **所有应用名称**: 提取的所有应用名称（逗号分隔）
- **所有包名**: 提取的所有应用包名（逗号分隔）
- **应用名称与包名对应关系**: JSON格式的配对关系
- **异常应用和包名**: JSON格式的异常应用与包名对应关系
- **异常应用**: 检测到的异常应用名称
- **异常包名原因**: 详细的异常判定原因

#### 工作表2：异常包名应用统计表（🔄重命名）
包含23种异常包名的统计信息：
- **应用和异常包名**: 应用名称：包名格式（🔄修改）
- **出现次数**: 在所有记录中的出现次数
- **占比百分比**: 在所有异常包名中的占比
- **检测规则**: 具体的检测规则说明

#### 工作表3：异常应用统计报告
包含3种异常应用关键词的统计信息：
- **异常应用关键词**: 检测规则中的关键词
- **命中次数**: 该关键词在所有记录中的出现次数
- **占比百分比**: 在所有异常应用记录中的占比

#### 工作表4：V3.5改进说明
包含版本改进的详细说明：
- **版本特点**: 修改统计报告表格式
- **重命名工作表**: 统计报告表 → 异常包名应用统计表
- **修改列名**: 异常包名 → 应用和异常包名
- **输出格式**: 应用名称：包名

### 样式特点
- 主数据表使用青绿色标题行
- 异常包名应用统计表使用红色标题行
- 异常应用统计报告使用蓝色标题行
- V3.5改进说明使用橙色标题行
- 优化的列宽设置，A列调整为80像素适应新格式
- 增加行高以适应内容显示

## 🛠️ 技术实现详情

### 核心改进算法

#### 1. **反向映射构建**
```python
# 从异常应用和包名JSON中提取映射关系
for result in results:
    if result['异常应用和包名'] and result['异常应用和包名'] != '{}':
        try:
            suspicious_mapping = json.loads(result['异常应用和包名'])
            for app_name, pkg_name in suspicious_mapping.items():
                # 建立包名到应用名称的映射
                if pkg_name not in pkg_to_app_mapping_for_stats:
                    pkg_to_app_mapping_for_stats[pkg_name] = []
                if app_name not in pkg_to_app_mapping_for_stats[pkg_name]:
                    pkg_to_app_mapping_for_stats[pkg_name].append(app_name)
        except:
            continue
```

#### 2. **格式化输出生成**
```python
# 生成"应用名称：包名"格式
if package in pkg_to_app_mapping_for_stats:
    app_names = pkg_to_app_mapping_for_stats[package]
    app_package_format = f"{app_names[0]}：{package}"  # 取第一个应用名称
else:
    app_package_format = f"未知应用：{package}"

package_statistics_data.append({
    '应用和异常包名': app_package_format,  # 新格式
    '出现次数': count,
    '占比百分比': f"{percentage:.2f}%",
    '检测规则': reason
})
```

#### 3. **Excel样式优化**
```python
# 列宽设置优化
'异常包名应用统计表': {
    'A': 80,  # 应用和异常包名（增加宽度）
    'B': 15,  # 出现次数
    'C': 15,  # 占比百分比
    'D': 80   # 检测规则
}
```

### 数据处理优化
1. **JSON解析**: 从异常应用和包名JSON中提取应用名称
2. **映射关系**: 建立包名到应用名称的反向映射
3. **格式统一**: 使用中文冒号分隔符保持格式一致性
4. **未知处理**: 对无法匹配应用名称的包名标记为"未知应用"
5. **多应用处理**: 当一个包名对应多个应用时，取第一个应用名称

### Excel输出优化
- **工作表重命名**: 更准确的工作表名称
- **列名更新**: 更直观的列名
- **列宽调整**: 适应新格式的列宽设置
- **样式保持**: 保持原有的颜色和样式体系

## 📊 版本演进对比

| 特性 | V3.4版本 | V3.5版本 | 改进效果 |
|------|----------|----------|----------|
| 工作表名称 | 统计报告表 | 异常包名应用统计表 | 更准确 |
| 列名 | 异常包名 | 应用和异常包名 | 更直观 |
| 输出格式 | 仅包名 | 应用名称：包名 | 信息更全 |
| 列宽设置 | 60像素 | 80像素 | 显示优化 |
| 用户体验 | 基础 | 优化 | 显著提升 |
| 信息完整性 | 一般 | 优秀 | 大幅改善 |

## 🎯 应用价值

### 1. **用户体验显著提升**
- **直观显示**: 直接显示应用名称与包名的对应关系
- **快速识别**: 用户可以快速识别异常应用的真实名称
- **减少查找**: 避免用户需要在不同列之间查找对应关系

### 2. **数据分析能力增强**
- **信息完整**: 在一列中包含应用名称和包名的完整信息
- **关系明确**: 应用名称与包名的关系一目了然
- **分析便利**: 便于进行应用级别的安全分析

### 3. **安全监控改进**
- **威胁识别**: 更容易识别伪装的恶意应用
- **风险评估**: 便于评估特定应用的安全风险
- **监控重点**: 明确监控重点应用和包名

### 4. **报告质量提升**
- **专业呈现**: 更专业的数据呈现方式
- **信息密度**: 在有限空间内提供更多有用信息
- **易于理解**: 非技术人员也能理解异常情况

## 🔧 使用建议

### 1. **数据查看**
- 重点关注"异常包名应用统计表"中的"应用和异常包名"列
- 注意应用名称与包名的一致性
- 关注出现频率高的异常应用

### 2. **安全分析**
- 优先分析应用名称与包名不匹配的记录
- 重点关注知名应用的异常包名
- 分析异常应用的分布模式

### 3. **威胁响应**
- 对高频出现的异常应用进行深入调查
- 验证应用名称的真实性
- 建立基于应用名称的监控规则

## 🎉 结论

V3.5版本成功实现了用户要求的所有改进：

1. **✅ 工作表重命名**: 成功将"统计报告表"重命名为"异常包名应用统计表"
2. **✅ 列名修改**: 成功将"异常包名"列改为"应用和异常包名"列
3. **✅ 输出格式**: 采用"应用名称：包名"格式，信息更直观
4. **✅ 技术实现**: 通过反向映射实现应用名称与包名的准确匹配
5. **✅ 样式优化**: 调整列宽和样式以适应新格式

V3.5版本在保持所有现有功能的基础上，通过改进统计报告表的格式，显著提升了数据的可读性和用户体验。新的"应用名称：包名"格式为用户提供了更直观、更完整的异常应用信息，是一次重要的用户体验优化。

---

**分析完成时间**: 2025-07-30 01:22:36  
**分析程序版本**: V3.5  
**输出文件**: `异常包名分析结果_V3.5_修正版.xlsx`  
**主要改进**: 统计报告表格式优化（应用名称：包名格式）  
**技术栈**: Python + pandas + openpyxl + JSON处理 + 反向映射算法
