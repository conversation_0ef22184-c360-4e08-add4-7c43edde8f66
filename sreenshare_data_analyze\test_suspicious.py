#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def is_suspicious_package_name(pkg_name):
    """
    判断是否为可疑的随机包名
    """
    if not pkg_name or pkg_name == 'N/A':
        return False
    
    print(f"检测包名: {pkg_name}")
    
    # 常见的正常包名前缀
    normal_prefixes = [
        'com.', 'cn.', 'org.', 'net.', 'edu.', 'gov.',
        'android.', 'androidx.', 'google.', 'microsoft.',
        'app.', 'io.', 'tv.', 'me.', 'de.', 'fr.', 'uk.'
    ]
    
    # 检查是否有正常前缀
    has_normal_prefix = any(pkg_name.startswith(prefix) for prefix in normal_prefixes)
    print(f"  有正常前缀: {has_normal_prefix}")
    
    # 特殊的随机字符串检测（针对具体的异常包名模式）
    # 如：com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi
    random_like_patterns = [
        r'[A-Z][a-z]+\d+[A-Z][a-z]+[A-Z]\d+[a-z]',  # 类似 DMey62ZwKlabJ48x 的模式
        r'[A-Z]{2,}[a-z]+[A-Z]{2,}[a-z]+[A-Z]',     # 类似 VSTOgjnDFyx5EZmi 的模式
        r'[A-Z][a-z]{2,}\d{2,}[A-Z][a-z]{2,}[A-Z]\d+[a-z]',  # 更精确的随机模式
        r'[A-Z]{1}[a-z]{3}\d{2}[A-Z]{1}[a-z]{2}[A-Z]{1}[a-z]{3}[A-Z]{1}\d{2}[a-z]{1}',  # DMey62ZwKlabJ48x 模式
        r'[A-Z]{3}[a-z]{4}[A-Z]{1}[a-z]{4}[A-Z]{1}[a-z]{2}[A-Z]{1}[a-z]{2}',  # VSTOgjnDFyx5EZmi 模式
    ]
    
    has_random_pattern = False
    for i, pattern in enumerate(random_like_patterns):
        if re.search(pattern, pkg_name):
            print(f"  匹配随机模式 {i+1}: {pattern}")
            has_random_pattern = True
    
    # 可疑特征检查
    suspicious_patterns = [
        # 包含大量随机字符
        r'[A-Z]{2,}[a-z]{2,}[A-Z]{2,}',  # 大小写混合的随机模式
        r'[a-zA-Z]{20,}',  # 超长的字符串
        r'\d{5,}',  # 包含5位以上数字
        # 特殊的随机字符组合
        r'[A-Z][a-z]{1,3}[A-Z][a-z]{1,3}[A-Z]',  # 如 AbCdEf 模式
        # 检查包名中是否包含明显的随机字符串段
        r'\.[A-Z][a-z]+\d+[A-Z][a-z]+',  # 如 .DMey62ZwKlab
        r'\.[A-Z]{3,}[a-z]+[A-Z]+[a-z]+',  # 如 .VSTOgjnDFyx
    ]
    
    # 检查可疑模式
    has_suspicious_pattern = False
    for i, pattern in enumerate(suspicious_patterns):
        if re.search(pattern, pkg_name):
            print(f"  匹配可疑模式 {i+1}: {pattern}")
            has_suspicious_pattern = True
    
    result = has_random_pattern or has_suspicious_pattern
    print(f"  最终结果: {result}")
    print()
    
    return result

# 测试包名
test_packages = [
    'com.ss.android.ugc.aweme',  # 正常包名
    'com.tencent.mm',  # 正常包名
    'com.DMey62ZwKlabJ48x.VSTOgjnDFyx5EZmi',  # 异常包名
    'com.qihoo.appstore',  # 正常包名
]

print("=== 测试异常包名检测 ===")
for pkg in test_packages:
    result = is_suspicious_package_name(pkg)
    print(f"包名 {pkg} 是否异常: {result}")
    print("-" * 50)
